#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证空格保留修复
"""

import sys
import os
sys.path.append('/root/semantic_analysis/sexual_content_dection')

# 模拟分词器解码，演示修复效果
def simulate_tokenizer_decode(text):
    """模拟分词器解码过程"""
    return text  # 简化模拟

def test_old_version():
    """测试旧版本的空格处理"""
    import re
    text = "这是一个 测试 文本，包含 多个 空格"
    
    # 模拟旧版本的处理方式
    old_result = re.sub(r"\s+", "", simulate_tokenizer_decode(text))
    print(f"旧版本处理结果: {repr(old_result)}")
    return old_result

def test_new_version():
    """测试新版本的空格处理"""
    text = "这是一个 测试 文本，包含 多个 空格"
    
    # 模拟新版本的处理方式
    new_result = simulate_tokenizer_decode(text)
    print(f"新版本处理结果: {repr(new_result)}")
    return new_result

def main():
    print("=== 空格保留修复验证 ===")
    print(f"原始文本: {repr('这是一个 测试 文本，包含 多个 空格')}")
    print()
    
    old_result = test_old_version()
    new_result = test_new_version()
    
    print()
    print("=== 对比结果 ===")
    print(f"旧版本去掉了空格: {' ' not in old_result}")
    print(f"新版本保留了空格: {' ' in new_result}")
    print(f"修复成功: {' ' in new_result and ' ' not in old_result}")

if __name__ == "__main__":
    main()
