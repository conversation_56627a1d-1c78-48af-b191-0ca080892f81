#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model/bert_env/lib/python3.10/site-packages')
from transformers import AutoTokenizer
import re

tokenizer = AutoTokenizer.from_pretrained('/root/semantic_analysis/sexual_content_dection/model/')

def improved_decode(tokenizer, tokens):
    """改进的解码方法，处理WordPiece子词"""
    vocab = tokenizer.get_vocab()
    id_to_token = {v: k for k, v in vocab.items()}
    token_strs = [id_to_token[token_id] for token_id in tokens]
    
    # 处理WordPiece子词标记
    result = []
    for token in token_strs:
        if token.startswith('##'):
            # 这是一个子词，直接拼接到前一个token
            result.append(token[2:])  # 去掉##前缀
        else:
            result.append(token)
    
    return ''.join(result)

# 测试改进的解码方法
test_cases = [
    '这是测试文本',
    '这是一个 包含空格的 文本',
    'english text',
    '中英混合mixed text',
    '测试123数字',
    'hello world',
    'programming',
    '这是一个测试programming混合文本'
]

print("=== 改进的解码方法测试 ===")
for i, text in enumerate(test_cases, 1):
    tokens = tokenizer.encode(text, add_special_tokens=False)
    
    # 查看tokens
    vocab = tokenizer.get_vocab()
    id_to_token = {v: k for k, v in vocab.items()}
    token_strs = [id_to_token[token_id] for token_id in tokens]
    
    # 原始decode方法
    original_decoded = tokenizer.decode(tokens)
    
    # 改进的解码方法
    improved_decoded = improved_decode(tokenizer, tokens)
    
    print(f"案例{i}: {repr(text)}")
    print(f"  tokens: {token_strs}")
    print(f"  原始解码: {repr(original_decoded)}")
    print(f"  改进解码: {repr(improved_decoded)}")
    print(f"  是否完全一致: {text == improved_decoded}")
    print()
