#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试空格保留功能
"""

import requests
import json

# API配置
BASE_URL = "http://localhost:7000"
LOGIN_URL = f"{BASE_URL}/api/auth/login"
DETECT_URL = f"{BASE_URL}/api/detect/text"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(LOGIN_URL, json=login_data)
    if response.status_code == 200:
        return response.json().get('token')
    else:
        print(f"登录失败: {response.status_code}, {response.text}")
        return None

def test_space_preservation():
    """测试空格保留功能"""
    token = login()
    if not token:
        print("无法获取token，测试终止")
        return
    
    # 测试文本，包含多个空格
    test_cases = [
        "这是一个 测试 文本，包含 多个 空格",
        "另一个  测试   文本，  包含    不规则空格",
        "正常文本without spaces",
        "  前后都有空格  ",
        "中间有\n换行\n符号"
    ]
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}:")
        print(f"原始文本: {repr(text)}")
        
        detect_data = {
            "text": text,
            "model_type": "keyword_only"
        }
        
        response = requests.post(DETECT_URL, json=detect_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"检测结果: {result.get('prediction', 'N/A')}")
            
            # 检查分段结果中是否保留了空格
            if 'segment_results' in result:
                for j, segment in enumerate(result['segment_results']):
                    segment_text = segment.get('text', '')
                    print(f"  段落 {j+1}: {repr(segment_text)}")
            else:
                print("  未找到分段结果")
        else:
            print(f"检测失败: {response.status_code}, {response.text}")

if __name__ == "__main__":
    test_space_preservation()
