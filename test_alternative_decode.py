#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model/bert_env/lib/python3.10/site-packages')
from transformers import AutoTokenizer

tokenizer = AutoTokenizer.from_pretrained('/root/semantic_analysis/sexual_content_dection/model/')

test_text = '这是测试文本'
tokens = tokenizer.encode(test_text, add_special_tokens=False)

print(f"原文: {repr(test_text)}")
print(f"tokens: {tokens}")
print()

# 方法1: 使用convert_tokens_to_string
print("=== 方法1: convert_tokens_to_string ===")
try:
    token_strs = tokenizer.convert_ids_to_tokens(tokens)
    print(f"token strings: {token_strs}")
    decoded1 = tokenizer.convert_tokens_to_string(token_strs)
    print(f"convert_tokens_to_string: {repr(decoded1)}")
except Exception as e:
    print(f"方法1失败: {e}")

# 方法2: 直接从vocab获取
print("\n=== 方法2: 直接从vocab获取 ===")
try:
    vocab = tokenizer.get_vocab()
    id_to_token = {v: k for k, v in vocab.items()}
    token_strs = [id_to_token[token_id] for token_id in tokens]
    print(f"直接token strings: {token_strs}")
    # 简单拼接
    decoded2 = ''.join(token_strs)
    print(f"直接拼接: {repr(decoded2)}")
except Exception as e:
    print(f"方法2失败: {e}")

# 方法3: 使用batch_decode
print("\n=== 方法3: batch_decode ===")
try:
    decoded3 = tokenizer.batch_decode([tokens], skip_special_tokens=True, clean_up_tokenization_spaces=False)[0]
    print(f"batch_decode: {repr(decoded3)}")
except Exception as e:
    print(f"方法3失败: {e}")

# 方法4: 检查tokenizer的内部方法
print("\n=== 方法4: 探索tokenizer内部方法 ===")
print(f"tokenizer type: {type(tokenizer)}")
print(f"available methods: {[m for m in dir(tokenizer) if 'decode' in m.lower()]}")
