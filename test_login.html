<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h2>登录测试</h2>
    <form id="loginForm">
        <div>
            <label>用户名: <input type="text" id="username" value="admin"></label>
        </div>
        <div>
            <label>密码: <input type="password" id="password" value="admin123"></label>
        </div>
        <button type="submit">登录</button>
    </form>
    
    <div id="result"></div>

    <script>
        // 配置axios
        axios.defaults.baseURL = 'http://localhost:7000';
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await axios.post('/api/auth/login', {
                    username,
                    password
                });
                
                resultDiv.innerHTML = '<h3>登录成功!</h3><pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
                resultDiv.style.color = 'green';
            } catch (error) {
                resultDiv.innerHTML = '<h3>登录失败!</h3><pre>' + JSON.stringify(error.response?.data || error.message, null, 2) + '</pre>';
                resultDiv.style.color = 'red';
            }
        });
    </script>
</body>
</html>
