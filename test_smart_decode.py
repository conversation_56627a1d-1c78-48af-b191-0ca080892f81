#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model/bert_env/lib/python3.10/site-packages')
from transformers import AutoTokenizer
import re

tokenizer = AutoTokenizer.from_pretrained('/root/semantic_analysis/sexual_content_dection/model/')

def smart_decode(tokenizer, tokens, original_text=None):
    """智能解码方法，尝试保留原始空格信息"""
    vocab = tokenizer.get_vocab()
    id_to_token = {v: k for k, v in vocab.items()}
    token_strs = [id_to_token[token_id] for token_id in tokens]
    
    # 处理WordPiece子词标记
    result = []
    for token in token_strs:
        if token.startswith('##'):
            result.append(token[2:])  # 去掉##前缀
        else:
            result.append(token)
    
    decoded = ''.join(result)
    
    # 如果有原始文本，尝试匹配空格位置
    if original_text:
        # 简单的启发式方法：如果原文有空格，尝试在合适位置插入
        if ' ' in original_text:
            # 找到原文中空格的大致位置
            words = original_text.split()
            if len(words) > 1:
                # 尝试在单词边界插入空格
                # 这是一个简化的方法，可能不完美
                pass
    
    return decoded

def alternative_approach(original_text):
    """另一种方法：只去掉tokenizer添加的多余空格"""
    if not original_text:
        return original_text
    
    # 对原始文本进行encode/decode
    tokens = tokenizer.encode(original_text, add_special_tokens=False)
    decoded_with_spaces = tokenizer.decode(tokens)
    
    # 如果原文没有空格，去掉所有空格
    if ' ' not in original_text:
        return decoded_with_spaces.replace(' ', '')
    
    # 如果原文有空格，需要更复杂的处理
    # 这里简化处理：保留原始空格数量
    original_spaces = original_text.count(' ')
    decoded_spaces = decoded_with_spaces.count(' ')
    
    if decoded_spaces > original_spaces:
        # 有多余空格，尝试去掉一些
        # 这是一个简化方法，实际情况可能需要更复杂的算法
        pass
    
    return decoded_with_spaces

# 测试不同的方法
test_cases = [
    '这是测试文本',
    '这是一个 包含空格的 文本',
    'english text',
    '中英混合mixed text',
]

print("=== 智能解码方法测试 ===")
for i, text in enumerate(test_cases, 1):
    tokens = tokenizer.encode(text, add_special_tokens=False)
    
    # 原始decode方法
    original_decoded = tokenizer.decode(tokens)
    
    # 智能解码方法
    smart_decoded = smart_decode(tokenizer, tokens, text)
    
    # 替代方法
    alternative_decoded = alternative_approach(text)
    
    print(f"案例{i}: {repr(text)}")
    print(f"  原始解码: {repr(original_decoded)}")
    print(f"  智能解码: {repr(smart_decoded)}")
    print(f"  替代方法: {repr(alternative_decoded)}")
    print()
