#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model/bert_env/lib/python3.10/site-packages')
from transformers import AutoTokenizer

tokenizer = AutoTokenizer.from_pretrained('/root/semantic_analysis/sexual_content_dection/model/')

def direct_decode(tokenizer, tokens):
    """直接解码方法，不添加空格"""
    vocab = tokenizer.get_vocab()
    id_to_token = {v: k for k, v in vocab.items()}
    token_strs = [id_to_token[token_id] for token_id in tokens]
    return ''.join(token_strs)

# 测试多种情况
test_cases = [
    '这是测试文本',
    '这是一个 包含空格的 文本',
    'english text',
    '中英混合mixed text',
    '测试123数字',
    '特殊符号\!@#$%'
]

print("=== 直接解码方法测试 ===")
for i, text in enumerate(test_cases, 1):
    tokens = tokenizer.encode(text, add_special_tokens=False)
    
    # 原始decode方法
    original_decoded = tokenizer.decode(tokens)
    
    # 直接解码方法
    direct_decoded = direct_decode(tokenizer, tokens)
    
    print(f"案例{i}: {repr(text)}")
    print(f"  原始解码: {repr(original_decoded)}")
    print(f"  直接解码: {repr(direct_decoded)}")
    print(f"  是否完全一致: {text == direct_decoded}")
    print()
