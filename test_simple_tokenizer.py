#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试tokenizer解码行为，不依赖transformers库
"""

import sys
import os
sys.path.append('/root/semantic_analysis/sexual_content_dection')

def analyze_tokenizer_behavior():
    """分析tokenizer解码可能的行为"""
    print("=== 分析Tokenizer解码行为 ===")
    
    # 基于BERT tokenizer的特性分析
    print("\n1. BERT Tokenizer特性:")
    print("   - 使用WordPiece tokenization")
    print("   - 中文字符通常被拆分为单个字符")
    print("   - 解码时可能在子词之间添加空格")
    
    # 模拟可能的解码行为
    test_cases = [
        ("这是测试", "这 是 测 试"),
        ("hello world", "hello world"),
        ("测试123", "测 试 1 2 3"),
        ("中英mixed", "中 英 mixed"),
    ]
    
    print("\n2. 可能的解码结果:")
    for original, possible_decoded in test_cases:
        print(f"   原文: {repr(original)}")
        print(f"   可能解码: {repr(possible_decoded)}")
        print(f"   是否添加空格: {len(possible_decoded) > len(original)}\n")

def check_current_implementation():
    """检查当前实现是否处理了tokenizer的空格问题"""
    print("=== 检查当前实现 ===")
    
    # 读取修改后的代码
    try:
        with open('/root/semantic_analysis/sexual_content_dection/model/batch_detect_with_keywords.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找相关代码行
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'tokenizer.decode' in line:
                print(f"第{i+1}行: {line.strip()}")
                
                # 检查前后几行的上下文
                start = max(0, i-3)
                end = min(len(lines), i+4)
                print("上下文:")
                for j in range(start, end):
                    marker = ">>> " if j == i else "    "
                    print(f"{marker}{j+1}: {lines[j]}")
                print()
                
    except Exception as e:
        print(f"无法读取文件: {e}")

def summarize_findings():
    """总结发现的问题"""
    print("=== 总结发现 ===")
    print()
    print("问题分析:")
    print("1. 原始代码: segment_text = re.sub(r'\\s+', '', tokenizer.decode(segment))")
    print("   - 这会删除tokenizer.decode()产生的所有空格")
    print("   - 包括原文中的空格和tokenizer添加的空格")
    print()
    print("2. 修改后代码: segment_text = tokenizer.decode(segment)")
    print("   - 保留了tokenizer.decode()的原始输出")
    print("   - 如果tokenizer会添加空格，现在会保留这些空格")
    print()
    print("3. 潜在影响:")
    print("   - 如果tokenizer会在中文字符间添加空格，现在这些空格会被保留")
    print("   - 这可能导致检测结果中出现不符合预期的空格")
    print("   - 需要进一步测试确认tokenizer的具体行为")
    print()
    print("建议:")
    print("   - 测试实际的tokenizer解码行为")
    print("   - 如果tokenizer确实会添加不必要的空格，考虑更精确的空格处理")
    print("   - 区分原文空格和tokenizer添加的空格")

if __name__ == "__main__":
    analyze_tokenizer_behavior()
    check_current_implementation()
    summarize_findings()
