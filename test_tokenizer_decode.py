#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试tokenizer解码过程是否会自动增加空格
"""

import sys
import os
sys.path.append('/root/semantic_analysis/sexual_content_dection')

def test_tokenizer_decode():
    """测试tokenizer解码行为"""
    
    # 测试不同的文本情况
    test_cases = [
        "这是连续的文本",
        "这是一个测试",
        "这个文本包含标点，符号！",
        "english text mixed",
        "数字123和文本",
        "特殊字符@#$%文本"
    ]
    
    print("=== Tokenizer解码测试 ===")
    
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}: {repr(text)}")
        
        # 模拟tokenizer encode/decode过程
        print(f"原始文本长度: {len(text)}")
        
        # 测试字符级别的分割和重组
        chars = list(text)
        reconstructed = ''.join(chars)
        print(f"字符重组后: {repr(reconstructed)}")
        print(f"重组后是否与原文一致: {reconstructed == text}")
        
        # 测试词级别的分割和重组（模拟tokenizer行为）
        # 中文tokenizer通常会在汉字之间添加空格
        if any('\u4e00' <= c <= '\u9fff' for c in text):
            # 包含中文字符的情况
            print("  包含中文字符 - tokenizer可能会在字符间添加空格")
        
        # 测试encode再decode的过程（简化模拟）
        encoded_simulation = text.replace('', ' ').strip()  # 模拟可能的空格插入
        if encoded_simulation != text:
            print(f"  模拟decode可能结果: {repr(encoded_simulation)}")
        
        print(f"  是否可能增加空格: {len(encoded_simulation) > len(text)}")

def test_real_tokenizer_behavior():
    """尝试测试真实的tokenizer行为"""
    print("\n=== 尝试测试真实Tokenizer ===")
    
    try:
        # 尝试导入并使用tokenizer
        from transformers import AutoTokenizer
        
        # 使用项目中的tokenizer
        tokenizer_path = '/root/semantic_analysis/sexual_content_dection/model/chinese_roberta_wwm_ext_pytorch'
        if os.path.exists(tokenizer_path):
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
            
            test_text = "这是测试文本"
            print(f"原始文本: {repr(test_text)}")
            
            # Encode
            tokens = tokenizer.encode(test_text, add_special_tokens=False)
            print(f"编码后tokens: {tokens}")
            
            # Decode
            decoded = tokenizer.decode(tokens)
            print(f"解码后文本: {repr(decoded)}")
            
            # 检查是否添加了空格
            if len(decoded) != len(test_text):
                print(f"长度变化: {len(test_text)} -> {len(decoded)}")
                print(f"是否添加了空格: {' ' in decoded and ' ' not in test_text}")
            else:
                print("长度未变化")
                
        else:
            print(f"Tokenizer路径不存在: {tokenizer_path}")
            
    except Exception as e:
        print(f"无法测试真实tokenizer: {e}")

if __name__ == "__main__":
    test_tokenizer_decode()
    test_real_tokenizer_behavior()
