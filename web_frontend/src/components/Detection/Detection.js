import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Input,
  Button,
  Upload,
  Table,
  Progress,
  Alert,
  Space,
  Tag,
  Modal,
  Descriptions,
  message,
  Row,
  Col,
  Statistic,
  List,
  Typography,
  Tooltip,
  Badge,
  Empty
} from 'antd';
import {
  PlayCircleOutlined,
  DownloadOutlined,
  EyeOutlined,
  FolderOpenOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  InboxOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  FileSearchOutlined,
  BarChartOutlined,
  FileExcelOutlined
} from '@ant-design/icons';
import axios from 'axios';
import * as XLSX from 'xlsx';

// TabPane 已弃用，直接使用 Tabs.TabPane 或 items 属性
const { TextArea } = Input;
const { Dragger } = Upload;
const { Text, Paragraph } = Typography;

const Detection = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('text');
  const [loading, setLoading] = useState(false);
  
  // 文本检测相关状态
  const [textContent, setTextContent] = useState('');
  const [textResult, setTextResult] = useState(null);
  
  // 批量检测相关状态
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [batchTasks, setBatchTasks] = useState(() => {
    // 从localStorage恢复批量任务状态
    const savedTasks = localStorage.getItem('batchDetectionTasks');
    return savedTasks ? JSON.parse(savedTasks) : [];
  });
  const [currentTask, setCurrentTask] = useState(() => {
    // 从localStorage恢复当前任务状态
    const savedCurrentTask = localStorage.getItem('currentDetectionTask');
    return savedCurrentTask ? JSON.parse(savedCurrentTask) : null;
  });

  // 结果详情模态框
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [detailData, setDetailData] = useState(null);
  const [detailActiveTab, setDetailActiveTab] = useState('summary');

  // 持久化批量任务状态到localStorage
  const saveBatchTasksToStorage = (tasks) => {
    localStorage.setItem('batchDetectionTasks', JSON.stringify(tasks));
  };

  // 持久化当前任务状态到localStorage
  const saveCurrentTaskToStorage = (task) => {
    if (task) {
      localStorage.setItem('currentDetectionTask', JSON.stringify(task));
    } else {
      localStorage.removeItem('currentDetectionTask');
    }
  };

  // 清理已完成的旧任务（保留最近10个）
  const cleanupOldTasks = (tasks) => {
    const completedTasks = tasks.filter(task => 
      ['completed', 'failed'].includes(task.status)
    );
    const runningTasks = tasks.filter(task => 
      ['pending', 'running'].includes(task.status)
    );
    
    // 保留所有运行中的任务 + 最近10个已完成的任务
    const recentCompletedTasks = completedTasks
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      .slice(0, 10);
    
    return [...runningTasks, ...recentCompletedTasks];
  };

  // 清理已完成的任务
  const clearCompletedTasks = () => {
    const runningTasks = batchTasks.filter(task => 
      ['pending', 'running'].includes(task.status)
    );
    setBatchTasks(runningTasks);
    message.success(`已清理${batchTasks.length - runningTasks.length}个已完成的任务`);
  };

  // 组件初始化
  useEffect(() => {
    // 清理旧任务
    const cleanedTasks = cleanupOldTasks(batchTasks);
    if (cleanedTasks.length !== batchTasks.length) {
      setBatchTasks(cleanedTasks);
      saveBatchTasksToStorage(cleanedTasks);
    }

    // 检查是否有未完成的任务需要恢复
    const runningTasks = batchTasks.filter(task => 
      ['pending', 'running'].includes(task.status)
    );
    
    if (runningTasks.length > 0) {
      message.info(`检测到${runningTasks.length}个未完成的任务，已自动恢复监控`);
      // 如果没有当前任务但有运行中的任务，设置第一个为当前任务
      if (!currentTask && runningTasks.length > 0) {
        setCurrentTask(runningTasks[0]);
      }
    }

    // 每5秒检查一次任务状态
    const interval = setInterval(checkTaskStatus, 5000);
    
    // 页面可见性变化监听
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // 页面变为可见时，立即检查任务状态
        checkTaskStatus();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 监听batchTasks变化，自动保存到localStorage
  useEffect(() => {
    saveBatchTasksToStorage(batchTasks);
  }, [batchTasks]);

  // 监听currentTask变化，自动保存到localStorage
  useEffect(() => {
    saveCurrentTaskToStorage(currentTask);
  }, [currentTask]);

  // 检查任务状态
  const checkTaskStatus = async () => {
    if (currentTask && ['pending', 'running'].includes(currentTask.status)) {
      try {
        const response = await axios.get(`/api/detect/task/${currentTask.id}`);
        const updatedTask = response.data;
        setCurrentTask(updatedTask);
        
        if (updatedTask.status === 'completed') {
          message.success('检测任务完成！');
          // 更新批量任务列表
          setBatchTasks(prev => prev.map(task => 
            task.id === updatedTask.id ? updatedTask : task
          ));
        } else if (updatedTask.status === 'failed') {
          message.error('检测任务失败: ' + updatedTask.error);
        }
      } catch (error) {
        console.error('检查任务状态失败:', error);
      }
    }
  };

  // 文本检测
  const handleTextDetection = async () => {
    if (!textContent.trim()) {
      message.warning('请输入要检测的文本内容');
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post('/api/detect/text', {
        text: textContent,
        async: false // 同步模式
      });
      
      setTextResult(response.data.result);
      message.success('文本检测完成');
    } catch (error) {
      message.error('文本检测失败: ' + error.response?.data?.error || error.message);
    } finally {
      setLoading(false);
    }
  };

  // 批量检测
  const handleBatchDetection = async () => {
    if (uploadedFiles.length === 0) {
      message.warning('请先上传要检测的文件');
      return;
    }

    setLoading(true);
    try {
      // 创建FormData对象
      const formData = new FormData();
      
      // 添加所有文件到FormData
      uploadedFiles.forEach((file, index) => {
        formData.append('files', file.originFileObj || file);
      });
      
      const response = await axios.post('/api/detect/batch-upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      const newTask = {
        id: response.data.task_id,
        status: 'pending',
        progress: 0,
        file_count: uploadedFiles.length,
        created_at: new Date().toISOString(),
        type: 'upload'
      };
      
      setBatchTasks(prev => [newTask, ...prev]);
      setCurrentTask(newTask);
      message.success(`已提交${uploadedFiles.length}个文件进行检测`);
    } catch (error) {
      message.error('批量检测失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 查看详细结果
  const showResultDetail = (result) => {
    setDetailData(result);
    setDetailActiveTab('summary');
    setDetailModalVisible(true);
  };

  // 导出检测结果（JSON格式）
  const exportResults = (result) => {
    try {
      const dataStr = JSON.stringify(result, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `detection_result_${new Date().getTime()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      message.success('检测结果已导出');
    } catch (error) {
      message.error('导出失败: ' + error.message);
    }
  };

  // 导出Excel格式结果
  const exportToExcel = (result) => {
    try {
      const excelData = [];

      // 遍历每个文件的检测结果
      if (result.details && Array.isArray(result.details)) {
        result.details.forEach(fileResult => {
          const fileName = fileResult.file_name || '未知文件';

          if (fileResult.error) {
            // 处理错误情况
            excelData.push({
              '文件名': fileName,
              '分段': 1,
              '分段内容': `错误: ${fileResult.error}`,
              '是否NSFW': '错误',
              '检测方法': '错误',
              '检测时间': fileResult.detected_at || new Date().toISOString(),
              '置信度': 0,
              '匹配关键词': ''
            });
          } else if (fileResult.segment_details && Array.isArray(fileResult.segment_details)) {
            // 处理正常的分段结果
            fileResult.segment_details.forEach(segment => {
              const detectionMethod = segment.detection_method === 'keyword' ? '关键词检测' :
                                    segment.detection_method === 'model' ? 'AI模型识别' :
                                    segment.detection_method || '未知';

              // 从关键词检测结果中获取匹配的关键词
              const keywordDetection = segment.keyword_detection || {};
              const matchedKeywords = [
                ...(keywordDetection.matched_blacklist || []),
                ...(keywordDetection.matched_whitelist || []),
                ...(keywordDetection.matched_keywords || [])
              ];

              // 处理高级规则匹配的关键词
              if (keywordDetection.matched_advanced_rules) {
                keywordDetection.matched_advanced_rules.forEach(rule => {
                  if (rule.matched_keywords) {
                    matchedKeywords.push(...rule.matched_keywords);
                  }
                });
              }

              excelData.push({
                '文件名': fileName,
                '分段': segment.segment_index || 1,
                '分段内容': segment.segment_text_full || segment.text || '',
                '是否NSFW': segment.is_nsfw ? '是' : '否',
                '检测方法': detectionMethod,
                '检测时间': fileResult.detected_at || new Date().toISOString(),
                '置信度': segment.nsfw_probability ? (segment.nsfw_probability * 100).toFixed(2) + '%' : '0%',
                '匹配关键词': matchedKeywords.join(', ')
              });
            });
          } else {
            // 处理没有分段信息的情况
            excelData.push({
              '文件名': fileName,
              '分段': 1,
              '分段内容': '无分段信息',
              '是否NSFW': fileResult.final_decision === 'NSFW' ? '是' : '否',
              '检测方法': '整体检测',
              '检测时间': fileResult.detected_at || new Date().toISOString(),
              '置信度': fileResult.confidence ? fileResult.confidence.toFixed(2) + '%' : '0%',
              '匹配关键词': (fileResult.matched_keywords || []).join(', ')
            });
          }
        });
      }

      // 如果没有数据，添加一个空行
      if (excelData.length === 0) {
        excelData.push({
          '文件名': '无数据',
          '分段': 1,
          '分段内容': '没有检测结果',
          '是否NSFW': '否',
          '检测方法': '无',
          '检测时间': new Date().toISOString(),
          '置信度': '0%',
          '匹配关键词': ''
        });
      }

      // 创建工作簿和工作表
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '检测结果');

      // 设置列宽
      const colWidths = [
        { wch: 20 }, // 文件名
        { wch: 8 },  // 分段
        { wch: 50 }, // 分段内容
        { wch: 12 }, // 是否NSFW
        { wch: 15 }, // 检测方法
        { wch: 20 }, // 检测时间
        { wch: 10 }, // 置信度
        { wch: 30 }  // 匹配关键词
      ];
      worksheet['!cols'] = colWidths;

      // 导出文件
      const fileName = `检测结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      message.success(`Excel文件已导出: ${fileName}`);
    } catch (error) {
      console.error('Excel导出错误:', error);
      message.error('Excel导出失败: ' + error.message);
    }
  };

  // 渲染检测结果标签
  const renderResultTag = (decision) => {
    const tagProps = {
      'SFW': { color: 'green', icon: <CheckCircleOutlined /> },
      'NSFW': { color: 'red', icon: <CloseCircleOutlined /> },
      'ERROR': { color: 'orange', icon: <ExclamationCircleOutlined /> }
    };
    
    const props = tagProps[decision] || { color: 'default' };
    return <Tag {...props}>{decision}</Tag>;
  };

  // 渲染置信度进度条
  const renderConfidenceBar = (confidence) => {
    const getColor = (value) => {
      if (value < 30) return '#52c41a';
      if (value < 70) return '#faad14';
      return '#ff4d4f';
    };
    
    return (
      <Progress 
        percent={confidence} 
        strokeColor={getColor(confidence)}
        size="small"
        format={(percent) => `${percent}%`}
      />
    );
  };

  // 渲染检测方法标签
  const renderDetectionMethodTag = (method, isNsfw = false) => {
    const methodConfig = {
      'keyword': { 
        color: isNsfw ? 'red' : 'green', 
        text: '关键词检测',
        icon: <FileSearchOutlined />
      },
      'segment_keyword': { 
        color: isNsfw ? 'red' : 'green', 
        text: '关键词检测',
        icon: <FileSearchOutlined />
      },
      'document_keyword': { 
        color: isNsfw ? 'red' : 'green', 
        text: '关键词检测',
        icon: <FileSearchOutlined />
      },
      'model': { 
        color: isNsfw ? 'volcano' : 'blue', 
        text: 'AI模型检测',
        icon: <BarChartOutlined />
      },
      'hybrid': {
        color: isNsfw ? 'purple' : 'geekblue',
        text: '混合检测',
        icon: <BarChartOutlined />
      }
    };
    
    const config = methodConfig[method] || { color: 'default', text: method || '未知', icon: null };
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 渲染NSFW片段内容
  const renderNsfwSegments = (segments) => {
    if (!segments || segments.length === 0) {
      return <Empty description="无分段信息" />;
    }

    const nsfwSegments = segments.filter(segment => segment.is_nsfw);
    
    if (nsfwSegments.length === 0) {
      return (
        <Alert
          message="未发现NSFW片段"
          description="所有分段都被判定为安全内容"
          type="success"
          showIcon
        />
      );
    }

    return (
      <div>
        <Alert
          message={`发现 ${nsfwSegments.length} 个NSFW片段`}
          type="warning"
          style={{ marginBottom: 16 }}
          showIcon
        />
        <List
          dataSource={nsfwSegments}
          renderItem={(segment, index) => (
            <List.Item>
              <Card 
                size="small" 
                style={{ width: '100%' }}
                title={
                  <div>
                    <Text strong>片段 {segment.segment_index}</Text>
                    {renderDetectionMethodTag(segment.detection_method, true)}
                    <Tag color="red" style={{ marginLeft: 8 }}>
                      置信度: {segment.nsfw_probability ? (segment.nsfw_probability * 100).toFixed(1) : '100.0'}%
                    </Tag>
                  </div>
                }
              >
                <Paragraph
                  style={{ 
                    backgroundColor: '#fff2f0', 
                    padding: '12px',
                    border: '1px solid #ffccc7',
                    borderRadius: '6px',
                    marginBottom: '12px'
                  }}
                >
                  <Text>{segment.text || segment.segment_text_full}</Text>
                </Paragraph>
                
                {segment.keyword_detection && 
                 segment.keyword_detection.matched_blacklist && 
                 segment.keyword_detection.matched_blacklist.length > 0 && (
                  <div>
                    <Text strong style={{ color: '#ff4d4f' }}>匹配的敏感关键词：</Text>
                    <div style={{ marginTop: 4 }}>
                      {segment.keyword_detection.matched_blacklist.map((keyword, idx) => (
                        <Tag key={idx} color="red" style={{ margin: '2px' }}>
                          {keyword}
                        </Tag>
                      ))}
                    </div>
                  </div>
                )}
              </Card>
            </List.Item>
          )}
        />
      </div>
    );
  };

  // 生成详情模态框的 tabs items
  const getDetailTabItems = (detailData) => {
    const items = [
      {
        key: 'summary',
        label: <span><BarChartOutlined />概览统计</span>,
        children: (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Card title="基本信息" size="small">
                  <Descriptions bordered size="small" column={1}>
                    {/* 批量检测结果 */}
                    {detailData.summary ? (
                      <>
                        <Descriptions.Item label="检测类型">
                          <Tag color="blue">批量检测</Tag>
                        </Descriptions.Item>
                        <Descriptions.Item label="完成时间">
                          {detailData.completed_at ? new Date(detailData.completed_at).toLocaleString() : '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="处理模式">
                          <Tag color={detailData.processing_mode === 'concurrent' ? 'green' : 'orange'}>
                            {detailData.processing_mode === 'concurrent' ? '并发处理' : '顺序处理'}
                          </Tag>
                        </Descriptions.Item>
                        {detailData.matched_keywords && detailData.matched_keywords.length > 0 && (
                          <Descriptions.Item label="匹配关键词">
                            <div>
                              {detailData.matched_keywords.slice(0, 3).map((keyword, index) => (
                                <Tag key={index} color="red" size="small" style={{ marginBottom: 4 }}>
                                  {keyword}
                                </Tag>
                              ))}
                              {detailData.matched_keywords.length > 3 && (
                                <Tag size="small" style={{ marginBottom: 4 }}>
                                  +{detailData.matched_keywords.length - 3}
                                </Tag>
                              )}
                            </div>
                          </Descriptions.Item>
                        )}
                      </>
                    ) : (
                      /* 单文件检测结果 */
                      <>
                        {detailData.final_decision && (
                          <Descriptions.Item label="最终结果">
                            {renderResultTag(detailData.final_decision)}
                          </Descriptions.Item>
                        )}
                        {detailData.confidence !== undefined && (
                          <Descriptions.Item label="置信度">
                            {renderConfidenceBar(detailData.confidence)}
                          </Descriptions.Item>
                        )}
                        {detailData.detected_at && (
                          <Descriptions.Item label="检测时间">
                            {new Date(detailData.detected_at).toLocaleString()}
                          </Descriptions.Item>
                        )}
                        {detailData.original_text_length && (
                          <Descriptions.Item label="文本长度">
                            {detailData.original_text_length.toLocaleString()} 字符
                          </Descriptions.Item>
                        )}
                        {/* 显示检测方法 */}
                        <Descriptions.Item label="检测方法">
                          {(() => {
                            // 优先使用文件级别的检测方法
                            if (detailData.detection_method) {
                              const methodMap = {
                                "混合检测": "hybrid",
                                "关键词检测": "keyword",
                                "AI模型检测": "model"
                              };
                              const methodKey = methodMap[detailData.detection_method] || 'model';
                              return renderDetectionMethodTag(methodKey, detailData.final_decision === 'NSFW');
                            }

                            // 如果有关键词优先级标记，显示关键词检测
                            if (detailData.keyword_priority) {
                              return renderDetectionMethodTag('keyword', detailData.final_decision === 'NSFW');
                            }

                            // 否则统计分段检测方法
                            const segments = detailData.segment_details || [];
                            const keywordCount = segments.filter(s => ['keyword', 'segment_keyword', 'document_keyword'].includes(s.detection_method)).length;
                            const modelCount = segments.filter(s => s.detection_method === 'model').length;

                            if (keywordCount > 0 && modelCount > 0) {
                              return (
                                <div>
                                  <Tag color="red" size="small">关键词: {keywordCount}</Tag>
                                  <Tag color="blue" size="small">模型: {modelCount}</Tag>
                                </div>
                              );
                            } else if (keywordCount > 0) {
                              return renderDetectionMethodTag('keyword', detailData.final_decision === 'NSFW');
                            } else if (modelCount > 0) {
                              return renderDetectionMethodTag('model', detailData.final_decision === 'NSFW');
                            } else {
                              return <Tag>未知</Tag>;
                            }
                          })()}
                        </Descriptions.Item>
                        {detailData.matched_keywords && detailData.matched_keywords.length > 0 && (
                          <Descriptions.Item label="匹配关键词">
                            <div>
                              {detailData.matched_keywords.slice(0, 3).map((keyword, index) => (
                                <Tag key={index} color="red" size="small" style={{ marginBottom: 4 }}>
                                  {keyword}
                                </Tag>
                              ))}
                              {detailData.matched_keywords.length > 3 && (
                                <Tag size="small" style={{ marginBottom: 4 }}>
                                  +{detailData.matched_keywords.length - 3}
                                </Tag>
                              )}
                            </div>
                          </Descriptions.Item>
                        )}
                      </>
                    )}
                  </Descriptions>
                </Card>
              </Col>

              <Col span={12}>
                {detailData.summary ? (
                  <Card title="批量检测汇总" size="small">
                    <Row gutter={16}>
                      <Col span={12}>
                        <Statistic
                          title="总文件数"
                          value={detailData.summary.total_files}
                          prefix={<FileTextOutlined />}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="NSFW比例"
                          value={detailData.summary.nsfw_percentage}
                          precision={1}
                          suffix="%"
                          valueStyle={{ color: '#ff4d4f' }}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="NSFW文件"
                          value={detailData.summary.nsfw_count}
                          valueStyle={{ color: '#ff4d4f' }}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="SFW文件"
                          value={detailData.summary.sfw_count}
                          valueStyle={{ color: '#52c41a' }}
                        />
                      </Col>
                    </Row>
                  </Card>
                ) : detailData.statistics && (
                  <Card title="分段统计" size="small">
                    <Row gutter={16}>
                      <Col span={12}>
                        <Statistic title="总分段" value={detailData.statistics.total_segments} />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="关键词命中率"
                          value={detailData.statistics.keyword_efficiency}
                          precision={1}
                          suffix="%"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="NSFW分段"
                          value={detailData.statistics.nsfw_segments}
                          valueStyle={{ color: '#ff4d4f' }}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="SFW分段"
                          value={detailData.statistics.sfw_segments}
                          valueStyle={{ color: '#52c41a' }}
                        />
                      </Col>
                    </Row>
                  </Card>
                )}
              </Col>
            </Row>

            {detailData.matched_keywords && detailData.matched_keywords.length > 0 && (
              <Card title="匹配关键词" size="small" style={{ marginTop: 16 }}>
                <div>
                  {detailData.matched_keywords.map((keyword, index) => (
                    <Tag key={index} color="red" style={{ margin: 4 }}>
                      {keyword}
                    </Tag>
                  ))}
                </div>
              </Card>
            )}
          </div>
        )
      }
    ];

    if (detailData.details) {
      items.push({
        key: 'files',
        label: <span><FileTextOutlined />文件详情</span>,
        children: renderFileDetails(detailData.details)
      });
    }

    if (detailData.segment_details) {
      items.push({
        key: 'segments',
        label: <span><InfoCircleOutlined />分段详情</span>,
        children: renderSegmentDetails(detailData.segment_details)
      });
    }

    if (detailData.segment_details && detailData.final_decision === 'NSFW') {
      items.push({
        key: 'nsfw_segments',
        label: <span><WarningOutlined />NSFW片段</span>,
        children: renderNsfwSegments(detailData.segment_details)
      });
    }

    if (detailData.error) {
      items.push({
        key: 'error',
        label: <span><WarningOutlined />错误信息</span>,
        children: (
          <Alert
            message="检测过程中发生错误"
            description={detailData.error}
            type="error"
            showIcon
          />
        )
      });
    }

    return items;
  };

  // 渲染分段详情
  const renderSegmentDetails = (segments) => {
    if (!segments || segments.length === 0) {
      return <Empty description="无分段详情" />;
    }

    return (
      <List
        dataSource={segments}
        renderItem={(segment, index) => (
          <List.Item>
            <Card 
              size="small" 
              style={{ 
                width: '100%',
                backgroundColor: segment.is_nsfw ? '#fff2f0' : '#f6ffed',
                border: segment.is_nsfw ? '1px solid #ffccc7' : '1px solid #b7eb8f'
              }}
            >
              <Row gutter={16}>
                <Col span={18}>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>分段 {segment.segment_index || (index + 1)}</Text>
                    <Tag 
                      color={segment.is_nsfw ? 'red' : 'green'} 
                      style={{ marginLeft: 8 }}
                    >
                      {segment.is_nsfw ? 'NSFW' : 'SFW'}
                    </Tag>
                    {renderDetectionMethodTag(segment.detection_method, segment.is_nsfw)}
                  </div>
                  <Paragraph 
                    ellipsis={{ rows: 3, expandable: true }}
                    style={{ marginTop: 8, marginBottom: 8 }}
                  >
                    {segment.text || segment.segment_text_full}
                  </Paragraph>
                  {segment.keyword_detection && segment.keyword_detection.matched_blacklist && 
                   segment.keyword_detection.matched_blacklist.length > 0 && (
                    <div>
                      <Text type="danger" style={{ fontSize: '12px' }}>
                        匹配关键词: {segment.keyword_detection.matched_blacklist.join(', ')}
                      </Text>
                    </div>
                  )}
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="置信度" 
                    value={segment.nsfw_probability ? (segment.nsfw_probability * 100).toFixed(1) : '0.0'}
                    suffix="%" 
                    valueStyle={{ 
                      fontSize: '14px',
                      color: segment.is_nsfw ? '#ff4d4f' : '#52c41a'
                    }}
                  />
                </Col>
              </Row>
            </Card>
          </List.Item>
        )}
      />
    );
  };

  // 渲染文件详情
  const renderFileDetails = (details) => {
    if (!details || details.length === 0) {
      return <Empty description="无文件详情" />;
    }



    const columns = [
      {
        title: '文件名',
        dataIndex: 'file_name',
        key: 'file_name',
        width: 180,
        render: (text) => (
          <Tooltip title={text}>
            <Text ellipsis style={{ maxWidth: 160 }}>
              <FileTextOutlined style={{ marginRight: 4 }} />
              {text}
            </Text>
          </Tooltip>
        )
      },
      {
        title: '检测结果',
        dataIndex: 'final_decision',
        key: 'final_decision',
        width: 100,
        render: (decision) => renderResultTag(decision)
      },
      {
        title: '检测方法',
        key: 'detection_method',
        width: 120,
        render: (_, record) => {
          if (record.error) return '-';
          
          // 优先使用文件级别的检测方法
          if (record.detection_method) {
              const methodMap = {
                "混合检测": "hybrid",
                "关键词检测": "keyword",
                "AI模型检测": "model"
              };
              const methodKey = methodMap[record.detection_method] || 'model';
              return renderDetectionMethodTag(methodKey, record.final_decision === 'NSFW');
          }

          // 统计检测方法
          const segments = record.segment_details || [];
          const keywordCount = segments.filter(s => ['keyword', 'segment_keyword', 'document_keyword'].includes(s.detection_method)).length;
          const modelCount = segments.filter(s => s.detection_method === 'model').length;
          
          if (keywordCount > 0 && modelCount > 0) {
            return (
              <div>
                <Tag color="red" size="small">关键词: {keywordCount}</Tag>
                <Tag color="blue" size="small">模型: {modelCount}</Tag>
              </div>
            );
          } else if (keywordCount > 0) {
            return renderDetectionMethodTag('keyword', record.final_decision === 'NSFW');
          } else if (modelCount > 0) {
            return renderDetectionMethodTag('model', record.final_decision === 'NSFW');
          } else {
            return '-';
          }
        }
      },
      {
        title: '置信度',
        dataIndex: 'confidence',
        key: 'confidence',
        width: 120,
        render: (confidence) => renderConfidenceBar(confidence || 0)
      },
      {
        title: '分段统计',
        key: 'segments',
        width: 150,
        render: (_, record) => {
          if (record.error) {
            return <Text type="danger">处理错误</Text>;
          }
          const stats = record.statistics;
          if (!stats) return '-';
          return (
            <Space direction="vertical" size={0}>
              <Text style={{ fontSize: '12px' }}>
                总计: {stats.total_segments}
              </Text>
              <Text style={{ fontSize: '12px' }}>
                <span style={{ color: '#ff4d4f' }}>NSFW: {stats.nsfw_segments}</span>
                {' / '}
                <span style={{ color: '#52c41a' }}>SFW: {stats.sfw_segments}</span>
              </Text>
            </Space>
          );
        }
      },
      {
        title: '关键词',
        dataIndex: 'matched_keywords',
        key: 'matched_keywords',
        render: (keywords, record) => {


          // 尝试从多个可能的位置获取关键词
          let allKeywords = keywords || [];

          // 如果没有文件级别的关键词，尝试从分段中收集
          if ((!allKeywords || allKeywords.length === 0) && record.segment_details) {
            const segmentKeywords = [];
            record.segment_details.forEach(segment => {
              const keywordDetection = segment.keyword_detection || {};
              if (keywordDetection.matched_blacklist) {
                segmentKeywords.push(...keywordDetection.matched_blacklist);
              }
              if (keywordDetection.matched_whitelist) {
                segmentKeywords.push(...keywordDetection.matched_whitelist);
              }
              if (keywordDetection.matched_keywords) {
                segmentKeywords.push(...keywordDetection.matched_keywords);
              }
              // 处理高级规则匹配的关键词
              if (keywordDetection.matched_advanced_rules) {
                keywordDetection.matched_advanced_rules.forEach(rule => {
                  if (rule.matched_keywords) {
                    segmentKeywords.push(...rule.matched_keywords);
                  }
                });
              }
            });
            allKeywords = [...new Set(segmentKeywords)]; // 去重
          }

          if (!allKeywords || allKeywords.length === 0) return '-';

          return (
            <div>
              {allKeywords.slice(0, 2).map((keyword, index) => (
                <Tag key={index} color="red" size="small">
                  {keyword}
                </Tag>
              ))}
              {allKeywords.length > 2 && (
                <Tag size="small">+{allKeywords.length - 2}</Tag>
              )}
            </div>
          );
        }
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (_, record) => (
          <Button 
            type="link" 
            icon={<FileSearchOutlined />}
            onClick={() => {
              setDetailData(record);
              setDetailActiveTab('segments');
            }}
            disabled={!!record.error}
          >
            详情
          </Button>
        )
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={details}
        rowKey="file_name"
        pagination={{ pageSize: 10 }}
        size="small"
        scroll={{ x: 900 }}
      />
    );
  };

  // 批量检测任务表格列配置
  const taskColumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      render: (text) => <span style={{ fontFamily: 'monospace' }}>{text.slice(0, 8)}...</span>
    },
    {
      title: '文件数量',
      dataIndex: 'file_count',
      key: 'file_count'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'pending': { color: 'blue', text: '等待中' },
          'running': { color: 'orange', text: '运行中' },
          'completed': { color: 'green', text: '已完成' },
          'failed': { color: 'red', text: '失败' }
        };
        const config = statusMap[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress) => <Progress percent={progress} size="small" />
    },
    {
      title: '检测结果',
      key: 'result_summary',
      render: (_, record) => {
        if (record.status !== 'completed' || !record.result) return '-';
        const summary = record.result.summary;
        if (!summary) return '-';
        
        return (
          <Space>
            <Badge count={summary.nsfw_count} style={{ backgroundColor: '#ff4d4f' }}>
              <Tag color="red">NSFW</Tag>
            </Badge>
            <Badge count={summary.sfw_count} style={{ backgroundColor: '#52c41a' }}>
              <Tag color="green">SFW</Tag>
            </Badge>
            {summary.error_count > 0 && (
              <Badge count={summary.error_count} style={{ backgroundColor: '#faad14' }}>
                <Tag color="orange">错误</Tag>
              </Badge>
            )}
          </Space>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {record.status === 'completed' && record.result && (
            <>
              <Button 
                type="link" 
                icon={<EyeOutlined />}
                onClick={() => showResultDetail(record.result)}
              >
                查看结果
              </Button>
              <Button
                type="link"
                icon={<DownloadOutlined />}
                onClick={() => exportResults(record.result)}
              >
                导出JSON
              </Button>
              <Button
                type="link"
                icon={<FileExcelOutlined />}
                onClick={() => exportToExcel(record.result)}
              >
                导出Excel
              </Button>
            </>
          )}
          <Button 
            type="link" 
            icon={<ReloadOutlined />}
            onClick={() => checkTaskStatus()}
          >
            刷新
          </Button>
        </Space>
      )
    }
  ];

  // 文件上传配置
  const uploadProps = {
    name: 'files',
    multiple: true,
    accept: '.txt,.doc,.docx,.pdf,.md',
    beforeUpload: (file) => {
      // 检查文件类型
      const allowedTypes = ['text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/pdf', 'text/markdown'];
      const isAllowedType = allowedTypes.includes(file.type) || file.name.endsWith('.txt') || file.name.endsWith('.md');
      
      if (!isAllowedType) {
        message.error(`${file.name} 不是支持的文件类型`);
        return false;
      }
      
      // 检查文件大小 (10MB)
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error(`${file.name} 文件大小超过10MB`);
        return false;
      }
      
      return false; // 阻止自动上传
    },
    onChange: (info) => {
      const { fileList } = info;
      setUploadedFiles(fileList);
    },
    onDrop: (e) => {
      console.log('拖拽文件:', e.dataTransfer.files);
    },
  };

  // 移除文件
  const removeFile = (file) => {
    const newFileList = uploadedFiles.filter(item => item.uid !== file.uid);
    setUploadedFiles(newFileList);
  };

  // 清空文件列表
  const clearFiles = () => {
    setUploadedFiles([]);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="内容检测" size="large">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'text',
              label: <span><FileTextOutlined />文本检测</span>,
              children: (
            <Row gutter={24}>
              <Col span={12}>
                <Card title="输入文本" size="small">
                  <TextArea
                    value={textContent}
                    onChange={(e) => setTextContent(e.target.value)}
                    placeholder="请输入要检测的文本内容..."
                    rows={10}
                    showCount
                    maxLength={10000}
                  />
                  <div style={{ marginTop: 16, textAlign: 'right' }}>
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      loading={loading}
                      onClick={handleTextDetection}
                      size="large"
                    >
                      开始检测
                    </Button>
                  </div>
                </Card>
              </Col>
              
              <Col span={12}>
                <Card title="检测结果" size="small">
                  {textResult ? (
                    <div>
                      <div style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={6}>
                            <Statistic
                              title="检测结果"
                              value={textResult.final_decision}
                              valueRender={() => renderResultTag(textResult.final_decision)}
                            />
                          </Col>
                          <Col span={6}>
                            <Statistic
                              title="置信度"
                              value={textResult.confidence}
                              precision={2}
                              suffix="%" 
                            />
                          </Col>
                          <Col span={6}>
                            <Statistic
                              title="分段数量"
                              value={textResult.statistics?.total_segments || 0}
                            />
                          </Col>
                          <Col span={6}>
                            <div>
                              <div style={{ fontSize: '14px', color: '#666', marginBottom: '4px' }}>检测方法</div>
                              {(() => {
                                // 优先使用文件级别的检测方法
                                if (textResult.detection_method) {
                                  const methodMap = {
                                    "混合检测": "hybrid",
                                    "关键词检测": "keyword",
                                    "AI模型检测": "model"
                                  };
                                  const methodKey = methodMap[textResult.detection_method] || 'model';
                                  return renderDetectionMethodTag(methodKey, textResult.final_decision === 'NSFW');
                                }

                                // 如果有关键词优先级标记，显示关键词检测
                                if (textResult.keyword_priority) {
                                  return renderDetectionMethodTag('keyword', textResult.final_decision === 'NSFW');
                                }

                                // 否则统计分段检测方法
                                const segments = textResult.segment_details || [];
                                const keywordCount = segments.filter(s => ['keyword', 'segment_keyword', 'document_keyword'].includes(s.detection_method)).length;
                                const modelCount = segments.filter(s => s.detection_method === 'model').length;

                                if (keywordCount > 0 && modelCount > 0) {
                                  return (
                                    <div>
                                      <Tag color="red" size="small">关键词: {keywordCount}</Tag>
                                      <Tag color="blue" size="small">模型: {modelCount}</Tag>
                                    </div>
                                  );
                                } else if (keywordCount > 0) {
                                  return renderDetectionMethodTag('keyword', textResult.final_decision === 'NSFW');
                                } else if (modelCount > 0) {
                                  return renderDetectionMethodTag('model', textResult.final_decision === 'NSFW');
                                } else {
                                  return <Tag>未知</Tag>;
                                }
                              })()}
                            </div>
                          </Col>
                        </Row>
                      </div>
                      
                      {textResult.matched_keywords && textResult.matched_keywords.length > 0 && (
                        <div style={{ marginBottom: 16 }}>
                          <strong>匹配关键词：</strong>
                          <div style={{ marginTop: 8 }}>
                            {textResult.matched_keywords.map((keyword, index) => (
                              <Tag key={index} color="red">{keyword}</Tag>
                            ))}
                          </div>
                        </div>
                      )}

                      {textResult.final_decision === 'NSFW' && textResult.segment_details && (
                        <div style={{ marginBottom: 16 }}>
                          <Alert
                            message={`检测到 ${textResult.segment_details.filter(s => s.is_nsfw).length} 个NSFW片段`}
                            type="warning"
                            showIcon
                            style={{ marginBottom: 12 }}
                          />
                          {textResult.segment_details
                            .filter(segment => segment.is_nsfw)
                            .slice(0, 2)
                            .map((segment, index) => (
                              <div key={index} style={{ 
                                backgroundColor: '#fff2f0', 
                                padding: '8px 12px', 
                                border: '1px solid #ffccc7',
                                borderRadius: '4px',
                                marginBottom: '8px'
                              }}>
                                <div style={{ marginBottom: '4px' }}>
                                  <Text strong>片段 {segment.segment_index}</Text>
                                  {renderDetectionMethodTag(segment.detection_method, true)}
                                </div>
                                <Text style={{ fontSize: '12px' }}>
                                  {(segment.text || segment.segment_text_full || '').substring(0, 100)}
                                  {(segment.text || segment.segment_text_full || '').length > 100 && '...'}
                                </Text>
                              </div>
                            ))}
                          {textResult.segment_details.filter(s => s.is_nsfw).length > 2 && (
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              还有 {textResult.segment_details.filter(s => s.is_nsfw).length - 2} 个NSFW片段，点击查看详细结果
                            </Text>
                          )}
                        </div>
                      )}
                      
                      <Button
                        type="link"
                        icon={<EyeOutlined />}
                        onClick={() => showResultDetail(textResult)}
                      >
                        查看详细结果
                      </Button>
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                      暂无检测结果
                    </div>
                  )}
                </Card>
              </Col>
            </Row>
              )
            },
            {
              key: 'batch',
              label: <span><FolderOpenOutlined />批量检测</span>,
              children: (
            <Row gutter={16}>
              <Col span={12}>
                <Card title="文件上传" size="small">
                  <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
                    <p className="ant-upload-drag-icon">
                      <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                    <p className="ant-upload-hint">
                      支持单个或批量上传。支持 .txt, .doc, .docx, .pdf, .md 格式文件，单个文件不超过10MB
                    </p>
                  </Dragger>
                  
                  {uploadedFiles.length > 0 && (
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <Text strong>已选择文件 ({uploadedFiles.length})</Text>
                        <Button 
                          type="link" 
                          size="small" 
                          icon={<DeleteOutlined />}
                          onClick={clearFiles}
                        >
                          清空
                        </Button>
                      </div>
                      <List
                        size="small"
                        bordered
                        dataSource={uploadedFiles}
                        renderItem={(file) => (
                          <List.Item
                            actions={[
                              <Button 
                                type="link" 
                                size="small" 
                                icon={<DeleteOutlined />}
                                onClick={() => removeFile(file)}
                              >
                                移除
                              </Button>
                            ]}
                          >
                            <List.Item.Meta
                              avatar={<FileTextOutlined />}
                              title={file.name}
                              description={`${(file.size / 1024).toFixed(1)} KB`}
                            />
                          </List.Item>
                        )}
                        style={{ maxHeight: 200, overflow: 'auto' }}
                      />
                    </div>
                  )}
                  
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={handleBatchDetection}
                    loading={loading}
                    disabled={uploadedFiles.length === 0}
                    style={{ marginTop: 16, width: '100%' }}
                  >
                    开始批量检测
                  </Button>
                </Card>
              </Col>

              <Col span={12}>
                <Card title="检测任务" size="small">
                  {/* 任务统计和操作 */}
                  <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Text type="secondary">
                        总任务: {batchTasks.length} | 
                        运行中: {batchTasks.filter(t => ['pending', 'running'].includes(t.status)).length} | 
                        已完成: {batchTasks.filter(t => t.status === 'completed').length} | 
                        失败: {batchTasks.filter(t => t.status === 'failed').length}
                      </Text>
                    </div>
                    <Space>
                      <Button 
                        type="link" 
                        icon={<ReloadOutlined />}
                        onClick={checkTaskStatus}
                        size="small"
                      >
                        刷新状态
                      </Button>
                      <Button 
                        type="link" 
                        icon={<DeleteOutlined />}
                        onClick={clearCompletedTasks}
                        disabled={batchTasks.filter(t => ['completed', 'failed'].includes(t.status)).length === 0}
                        size="small"
                      >
                        清理已完成
                      </Button>
                    </Space>
                  </div>
                  
                  {currentTask && ['pending', 'running'].includes(currentTask.status) && (
                    <Alert
                      message={`当前任务进行中: ${currentTask.id.slice(0, 8)}...`}
                      description={
                        <div>
                          <Progress percent={currentTask.progress} />
                          <div style={{ marginTop: 8 }}>
                            文件数量: {currentTask.file_count}
                          </div>
                        </div>
                      }
                      type="info"
                      style={{ marginBottom: 16 }}
                    />
                  )}
                  
                  <Table
                    columns={taskColumns}
                    dataSource={batchTasks}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                    size="small"
                  />
                </Card>
              </Col>
            </Row>
              )
            }
          ]}
        />
      </Card>

      {/* 详细结果模态框 */}
      <Modal
        title={
          <div>
            <FileSearchOutlined style={{ marginRight: 8 }} />
            检测结果详情
          </div>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        width={1200}
        footer={[
          <Button
            key="export-json"
            icon={<DownloadOutlined />}
            onClick={() => exportResults(detailData)}
          >
            导出JSON
          </Button>,
          <Button
            key="export-excel"
            icon={<FileExcelOutlined />}
            onClick={() => exportToExcel(detailData)}
            type="primary"
          >
            导出Excel
          </Button>,
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        {detailData && (
          <Tabs
            activeKey={detailActiveTab}
            onChange={setDetailActiveTab}
            items={getDetailTabItems(detailData)}
          />
        )}
      </Modal>
    </div>
  );
};

export default Detection;