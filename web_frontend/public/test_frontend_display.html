<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background-color: #f6ffed; border: 1px solid #b7eb8f; }
        .error { background-color: #fff2f0; border: 1px solid #ffccc7; }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover { background-color: #40a9ff; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th { background-color: #f5f5f5; }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin: 2px;
        }
        .tag-red { background-color: #ff4d4f; color: white; }
        .tag-blue { background-color: #1890ff; color: white; }
        .tag-green { background-color: #52c41a; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端显示测试</h1>
        
        <div class="test-section">
            <h3>测试1: 获取最新任务结果</h3>
            <button onclick="getLatestTaskResult()">获取最新任务结果</button>
            <div id="latest-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 模拟前端数据处理</h3>
            <button onclick="simulateFrontendProcessing()">模拟前端处理</button>
            <div id="frontend-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 直接显示表格</h3>
            <button onclick="displayTable()">显示表格</button>
            <div id="table-container"></div>
        </div>
    </div>

    <script>
        let authToken = '';
        let latestTaskData = null;

        // 登录获取token
        async function login() {
            try {
                const response = await fetch('http://*************:7000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                if (response.ok) {
                    authToken = data.access_token;
                    console.log('✅ 登录成功');
                    return true;
                } else {
                    console.error('❌ 登录失败:', data);
                    return false;
                }
            } catch (error) {
                console.error('❌ 登录错误:', error);
                return false;
            }
        }

        // 获取最新任务结果
        async function getLatestTaskResult() {
            const resultDiv = document.getElementById('latest-result');
            
            if (!authToken) {
                const loginSuccess = await login();
                if (!loginSuccess) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 登录失败';
                    return;
                }
            }
            
            try {
                // 使用已知的任务ID
                const taskId = '1c27b3a0-9d4e-48a7-b8cf-88d86d290ac3';
                const response = await fetch(`http://*************:7000/api/detect/task/${taskId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.result) {
                    latestTaskData = data.result;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 获取成功\n${JSON.stringify(data.result, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 获取错误: ${error.message}`;
            }
        }

        // 模拟前端数据处理
        function simulateFrontendProcessing() {
            const resultDiv = document.getElementById('frontend-result');
            
            if (!latestTaskData) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请先获取任务结果';
                return;
            }
            
            try {
                console.log('🔍 模拟前端处理 - 原始数据:', latestTaskData);
                
                if (latestTaskData.details && latestTaskData.details.length > 0) {
                    const details = latestTaskData.details;
                    console.log('📁 文件详情:', details);
                    
                    let processedData = [];
                    details.forEach((detail, index) => {
                        console.log(`📄 处理文件 ${index + 1}:`, detail);
                        
                        const processed = {
                            file_name: detail.file_name,
                            detection_method: detail.detection_method,
                            keyword_priority: detail.keyword_priority,
                            matched_keywords: detail.matched_keywords,
                            final_decision: detail.final_decision,
                            confidence: detail.confidence
                        };
                        
                        processedData.push(processed);
                        console.log(`📄 处理后的数据 ${index + 1}:`, processed);
                    });
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 处理成功\n处理后的数据:\n${JSON.stringify(processedData, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 没有找到details数据';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 处理错误: ${error.message}`;
            }
        }

        // 显示表格
        function displayTable() {
            const container = document.getElementById('table-container');
            
            if (!latestTaskData || !latestTaskData.details) {
                container.innerHTML = '<div class="result error">❌ 请先获取任务结果</div>';
                return;
            }
            
            try {
                const details = latestTaskData.details;
                
                let tableHtml = `
                <table>
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>检测结果</th>
                            <th>置信度</th>
                            <th>检测方法</th>
                            <th>匹配关键词</th>
                        </tr>
                    </thead>
                    <tbody>`;
                
                details.forEach(detail => {
                    console.log('🔍 渲染表格行:', detail);
                    
                    const fileName = detail.file_name || '未知文件';
                    const finalDecision = detail.final_decision || '未知';
                    const confidence = detail.confidence ? detail.confidence.toFixed(2) + '%' : '0%';
                    const detectionMethod = detail.detection_method || '未知';
                    const matchedKeywords = (detail.matched_keywords || []).join(', ') || '无';
                    
                    tableHtml += `
                    <tr>
                        <td>${fileName}</td>
                        <td><span class="tag tag-${finalDecision === 'NSFW' ? 'red' : 'green'}">${finalDecision}</span></td>
                        <td>${confidence}</td>
                        <td><span class="tag tag-blue">${detectionMethod}</span></td>
                        <td>${matchedKeywords}</td>
                    </tr>`;
                });
                
                tableHtml += `
                    </tbody>
                </table>`;
                
                container.innerHTML = tableHtml;
            } catch (error) {
                container.innerHTML = `<div class="result error">❌ 表格渲染错误: ${error.message}</div>`;
            }
        }

        // 页面加载时自动登录
        window.onload = function() {
            login();
        };
    </script>
</body>
</html>
