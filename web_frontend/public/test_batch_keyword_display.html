<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量检测关键词显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .tag {
            display: inline-block;
            padding: 2px 6px;
            margin: 2px;
            border-radius: 3px;
            font-size: 12px;
        }
        .tag-red {
            background-color: #ff4d4f;
            color: white;
        }
        .tag-blue {
            background-color: #1890ff;
            color: white;
        }
        .tag-green {
            background-color: #52c41a;
            color: white;
        }
    </style>
</head>
<body>
    <h1>批量检测关键词显示测试</h1>
    
    <div class="test-section">
        <h3>1. 登录测试</h3>
        <button onclick="testLogin()">测试登录</button>
        <div id="login-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 批量文件检测测试</h3>
        <input type="file" id="file-input" multiple accept=".txt,.doc,.docx">
        <button onclick="testBatchDetection()">批量检测</button>
        <div id="batch-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 检测结果详情模拟</h3>
        <button onclick="simulateDetailView()">模拟详情显示</button>
        <div id="detail-result" class="result"></div>
    </div>

    <script>
        let authToken = null;
        let lastBatchResult = null;

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            try {
                const response = await fetch('http://*************:7000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.access_token;
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 登录成功`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ 登录失败: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 登录错误: ${error.message}`;
            }
        }

        async function testBatchDetection() {
            const resultDiv = document.getElementById('batch-result');
            const fileInput = document.getElementById('file-input');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ 请先登录';
                return;
            }
            
            if (!fileInput.files.length) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ 请选择文件';
                return;
            }
            
            try {
                const formData = new FormData();
                for (let file of fileInput.files) {
                    formData.append('files', file);
                }
                
                const response = await fetch('http://*************:7000/api/detect/batch-upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 批量检测任务创建成功\n任务ID: ${data.task_id}`;
                    
                    // 等待任务完成
                    setTimeout(() => checkTaskResult(data.task_id), 3000);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ 批量检测失败: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 批量检测错误: ${error.message}`;
            }
        }

        async function checkTaskResult(taskId) {
            const resultDiv = document.getElementById('batch-result');
            
            try {
                const response = await fetch(`http://*************:7000/api/detect/task/${taskId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.status === 'completed' && data.result) {
                    lastBatchResult = data.result;
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 任务完成\n${formatBatchResult(data.result)}`;
                    
                    // 显示文件详情表格
                    displayFileDetails(data.result.details);
                } else if (data.status === 'running') {
                    setTimeout(() => checkTaskResult(taskId), 2000);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ 任务失败: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 查询错误: ${error.message}`;
            }
        }

        function formatBatchResult(result) {
            return `批量检测结果:
总文件数: ${result.summary.total_files}
NSFW文件: ${result.summary.nsfw_count}
关键词优先级文件: ${result.summary.keyword_priority_count}
匹配关键词: ${result.matched_keywords ? result.matched_keywords.join(', ') : '无'}`;
        }

        function displayFileDetails(details) {
            const resultDiv = document.getElementById('batch-result');
            
            let tableHtml = `
            <h4>文件详情表格:</h4>
            <table>
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>检测结果</th>
                        <th>置信度</th>
                        <th>检测方法</th>
                        <th>匹配关键词</th>
                    </tr>
                </thead>
                <tbody>`;
            
            details.forEach(detail => {
                // 模拟前端的检测方法渲染逻辑
                let detectionMethodHtml = '';
                console.log('🔍 处理文件:', detail.file_name, {
                    detection_method: detail.detection_method,
                    keyword_priority: detail.keyword_priority,
                    matched_keywords: detail.matched_keywords
                });
                
                if (detail.detection_method) {
                    const methodMap = {
                        "混合检测": "hybrid",
                        "关键词检测": "keyword",
                        "AI模型检测": "model"
                    };
                    const methodKey = methodMap[detail.detection_method] || 'model';
                    const color = methodKey === 'keyword' ? 'red' : methodKey === 'model' ? 'blue' : 'green';
                    detectionMethodHtml = `<span class="tag tag-${color}">${detail.detection_method}</span>`;
                } else if (detail.keyword_priority) {
                    detectionMethodHtml = `<span class="tag tag-red">关键词检测</span>`;
                } else {
                    detectionMethodHtml = `<span class="tag tag-blue">AI模型检测</span>`;
                }
                
                // 匹配关键词
                let keywordsHtml = '';
                if (detail.matched_keywords && detail.matched_keywords.length > 0) {
                    keywordsHtml = detail.matched_keywords.slice(0, 3).map(keyword => 
                        `<span class="tag tag-red">${keyword}</span>`
                    ).join('');
                    if (detail.matched_keywords.length > 3) {
                        keywordsHtml += `<span class="tag">+${detail.matched_keywords.length - 3}</span>`;
                    }
                } else {
                    keywordsHtml = '-';
                }
                
                tableHtml += `
                    <tr>
                        <td>${detail.file_name}</td>
                        <td><span class="tag tag-${detail.final_decision === 'NSFW' ? 'red' : 'green'}">${detail.final_decision}</span></td>
                        <td>${detail.confidence}%</td>
                        <td>${detectionMethodHtml}</td>
                        <td>${keywordsHtml}</td>
                    </tr>`;
            });
            
            tableHtml += `
                </tbody>
            </table>`;
            
            resultDiv.innerHTML += tableHtml;
        }

        function simulateDetailView() {
            const resultDiv = document.getElementById('detail-result');
            
            if (!lastBatchResult) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ 请先进行批量检测';
                return;
            }
            
            // 模拟前端的详情显示逻辑
            console.log('🔍 模拟详情显示 - 原始数据:', lastBatchResult);
            
            let detailHtml = `
            <h4>批量检测详情:</h4>
            <p><strong>检测类型:</strong> 批量检测</p>
            <p><strong>处理模式:</strong> ${lastBatchResult.processing_mode}</p>
            <p><strong>完成时间:</strong> ${new Date(lastBatchResult.completed_at).toLocaleString()}</p>`;
            
            if (lastBatchResult.matched_keywords && lastBatchResult.matched_keywords.length > 0) {
                detailHtml += `<p><strong>匹配关键词:</strong> `;
                lastBatchResult.matched_keywords.slice(0, 3).forEach(keyword => {
                    detailHtml += `<span class="tag tag-red">${keyword}</span>`;
                });
                if (lastBatchResult.matched_keywords.length > 3) {
                    detailHtml += `<span class="tag">+${lastBatchResult.matched_keywords.length - 3}</span>`;
                }
                detailHtml += `</p>`;
            }
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = detailHtml;
        }

        // 页面加载时自动登录
        window.onload = function() {
            testLogin();
        };
    </script>
</body>
</html>
