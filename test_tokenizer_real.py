#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model/bert_env/lib/python3.10/site-packages')
from transformers import AutoTokenizer

tokenizer = AutoTokenizer.from_pretrained('/root/semantic_analysis/sexual_content_dection/model/')

test_cases = [
    '这是测试文本',
    '这是一个 包含空格的 文本',
    'english text',
    '中英混合mixed text',
    '测试123数字'
]

print("=== 实际Tokenizer测试结果 ===")
for i, text in enumerate(test_cases, 1):
    tokens = tokenizer.encode(text, add_special_tokens=False)
    decoded = tokenizer.decode(tokens)
    print(f"案例{i}:")
    print(f"  原文: {repr(text)}")
    print(f"  解码: {repr(decoded)}")
    print(f"  空格数量: {text.count(' ')} -> {decoded.count(' ')}")
    print(f"  是否添加了空格: {decoded.count(' ') > text.count(' ')}")
    print()
