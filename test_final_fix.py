#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复后的效果
"""

import sys
sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model/bert_env/lib/python3.10/site-packages')
sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model')

from batch_detect_with_keywords import split_long_text
from transformers import AutoTokenizer

# 初始化tokenizer
tokenizer = AutoTokenizer.from_pretrained('/root/semantic_analysis/sexual_content_dection/model/')

# 测试案例
test_cases = [
    '这是测试文本',
    '这是一个 包含空格的 文本',
    '测试123数字',
    '特殊符号\!@#$%文本',
    '这是一个很长的文本，需要进行分段处理，看看是否能保持原始的空格格式',
    '短文本',
    '这是 一个 有 多个 空格 的 文本'
]

print("=== 最终修复效果测试 ===")
for i, text in enumerate(test_cases, 1):
    print(f"\n案例{i}: {repr(text)}")
    
    # 使用修复后的split_long_text函数
    segments = split_long_text(text, tokenizer, max_length=50)
    
    print(f"  分段数量: {len(segments)}")
    for j, segment in enumerate(segments):
        print(f"  段落{j+1}: {repr(segment)}")
    
    # 检查是否保留了原始空格（对于纯中文文本）
    if len(segments) == 1:
        is_chinese_only = all('\u4e00' <= c <= '\u9fff' or c.isdigit() or c in '\!@#$%' for c in text.replace(' ', ''))
        if is_chinese_only:
            print(f"  纯中文文本匹配: {text == segments[0]}")
        else:
            print(f"  包含英文/混合文本")
