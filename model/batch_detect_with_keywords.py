import os
import json
import torch
import re
from transformers import BertTokenizer, BertForSequenceClassification
from tqdm import tqdm
from datetime import datetime

# 本地环境路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 项目根目录
BASE_MODEL_PATH = os.path.join(BASE_DIR, "model")
TEST_DIR = os.path.join(BASE_DIR, "content")  # 本地输入目录
RESULTS_DIR = os.path.join(BASE_DIR, "detection_results")  # 本地结果目录

print(f"📁 项目根目录: {BASE_DIR}")
print(f"📁 模型目录: {BASE_MODEL_PATH}")
print(f"📁 输入目录: {TEST_DIR}")
print(f"📁 结果目录: {RESULTS_DIR}")

# 确保必要目录存在
os.makedirs(TEST_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)

# 动态生成结果文件名
now_str = datetime.now().strftime("%Y%m%d_%H%M%S")
RESULTS_JSON = os.path.join(RESULTS_DIR, f"detection_results_keywords_{now_str}.json")

# 长文本处理配置
MAX_LENGTH = 512  # BERT最大输入长度
OVERLAP_LENGTH = 50  # 分段重叠长度，避免边界信息丢失
MIN_SEGMENT_LENGTH = 150  # 最小分段长度（推荐平衡策略）

class KeywordDetector:
    """关键词检测器"""
    
    def __init__(self, config_file):
        self.config_file = config_file
        self.config = self.load_config()
        self.enabled = self.config.get("config", {}).get("enabled", True)
        self.case_sensitive = self.config.get("config", {}).get("case_sensitive", False)
        
    def load_config(self):
        """加载关键词配置"""
        try:
            with open(self.config_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️  关键词配置文件加载失败: {e}")
            return {"config": {"enabled": False}}
    
    def check_simple_keywords(self, text, keywords, logic="OR"):
        """检查简单关键词匹配"""
        if not keywords:
            return False, []

        matched_keywords = []
        # 移除空格以处理tokenizer decode产生的空格问题
        text_to_check = text.replace(' ', '') if self.case_sensitive else text.replace(' ', '').lower()

        for keyword in keywords:
            if isinstance(keyword, str):
                keyword_to_check = keyword.replace(' ', '') if self.case_sensitive else keyword.replace(' ', '').lower()
                if keyword_to_check in text_to_check:
                    matched_keywords.append(keyword)
        
        if not isinstance(logic, str):
            logic = "OR"  # 默认值
        
        if logic == "OR":
            return len(matched_keywords) > 0, matched_keywords
        elif logic == "AND":
            return len(matched_keywords) == len(keywords), matched_keywords
        else:
            return False, []
    
    def check_advanced_rules(self, text, rules):
        """检查高级规则匹配"""
        matched_rules = []
        
        for rule in rules:
            rule_keywords = rule.get("keywords", [])
            rule_logic = rule.get("logic", "OR")
            rule_description = rule.get("description", "")
            
            is_match, matched_kw = self.check_simple_keywords(text, rule_keywords, rule_logic)
            
            if is_match:
                matched_rules.append({
                    "description": rule_description,
                    "matched_keywords": matched_kw,
                    "logic": rule_logic,
                    "weight": rule.get("weight", 1.0)
                })
        
        return len(matched_rules) > 0, matched_rules
    
    def check_context_rules(self, text):
        """检查上下文规则"""
        context_rules = self.config.get("context_rules", {})
        if not context_rules.get("enabled", False):
            return []
        
        exemptions = []
        rules = context_rules.get("rules", [])
        
        if not isinstance(rules, (list, tuple)):
            rules = []
        
        for rule in rules:
            context_keywords = rule.get("context_keywords", [])
            exempt_keywords = rule.get("exempt_keywords", [])
            
            # 检查是否存在上下文关键词
            has_context, _ = self.check_simple_keywords(text, context_keywords, "OR")
            
            if has_context:
                # 检查豁免关键词
                has_exempt, matched_exempt = self.check_simple_keywords(text, exempt_keywords, "OR")
                if has_exempt:
                    exemptions.append({
                        "rule_name": rule.get("name", ""),
                        "description": rule.get("description", ""),
                        "exempt_keywords": matched_exempt
                    })
        
        return exemptions
    
    def detect(self, text):
        """主检测函数"""
        if not self.enabled:
            return {
                "keyword_enabled": False,
                "keyword_result": None,
                "keyword_priority": False
            }
        
        result = {
            "keyword_enabled": True,
            "keyword_result": None,
            "keyword_priority": False,
            "matched_blacklist": [],
            "matched_whitelist": [],
            "matched_advanced_rules": [],
            "context_exemptions": [],
            "final_decision": None
        }
        
        # 检查白名单（优先级最高）
        whitelist_config = self.config.get("whitelist", {})
        whitelist_keywords = whitelist_config.get("keywords", [])
        whitelist_logic = whitelist_config.get("logic", "OR")
        if not isinstance(whitelist_logic, str):
            whitelist_logic = "OR"
        
        is_whitelist, matched_whitelist = self.check_simple_keywords(text, whitelist_keywords, whitelist_logic)
        
        # 检查白名单高级规则
        whitelist_advanced = whitelist_config.get("advanced_rules", [])
        is_whitelist_advanced, matched_whitelist_advanced = self.check_advanced_rules(text, whitelist_advanced)
        
        if is_whitelist or is_whitelist_advanced:
            result["keyword_result"] = "SFW"
            result["keyword_priority"] = True
            result["matched_whitelist"] = matched_whitelist
            result["matched_advanced_rules"].extend(matched_whitelist_advanced)
            result["final_decision"] = "SFW"
            return result
        
        # 检查上下文豁免规则
        context_exemptions = self.check_context_rules(text)
        result["context_exemptions"] = context_exemptions
        
        # 检查黑名单
        blacklist_config = self.config.get("blacklist", {})
        blacklist_keywords = blacklist_config.get("keywords", [])
        blacklist_logic = blacklist_config.get("logic", "OR")
        if not isinstance(blacklist_logic, str):
            blacklist_logic = "OR"
        
        is_blacklist, matched_blacklist = self.check_simple_keywords(text, blacklist_keywords, blacklist_logic)
        
        # 检查黑名单高级规则
        blacklist_advanced = blacklist_config.get("advanced_rules", [])
        is_blacklist_advanced, matched_blacklist_advanced = self.check_advanced_rules(text, blacklist_advanced)
        
        # 应用上下文豁免
        exempt_keywords = []
        for exemption in context_exemptions:
            exempt_keywords.extend(exemption["exempt_keywords"])
        
        # 过滤掉被豁免的关键词
        final_matched_blacklist = [kw for kw in matched_blacklist if kw not in exempt_keywords]
        
        if (is_blacklist and final_matched_blacklist) or is_blacklist_advanced:
            result["keyword_result"] = "NSFW"
            result["keyword_priority"] = True
            result["matched_blacklist"] = final_matched_blacklist
            result["matched_advanced_rules"].extend(matched_blacklist_advanced)
            result["final_decision"] = "NSFW"
            return result
        
        # 没有命中关键词规则
        result["keyword_result"] = None
        result["keyword_priority"] = False
        result["final_decision"] = None
        return result

def split_long_text(text, tokenizer, max_length=512, overlap=50, min_length=150):
    """
    将长文本分割成多个短文本段落，支持更短的分段长度
    
    Args:
        text: 输入文本
        tokenizer: 分词器
        max_length: 每个段落的最大token长度
        overlap: 相邻段落的重叠token数
        min_length: 最小段落长度（字符数）
        
    Returns:
        segments: 分割后的文本段落列表
    """
    if not text or not text.strip():
        return []
    
    # 如果文本长度小于最小长度，直接返回
    if len(text) <= min_length:
        return [text]
    
    # 对于极短的max_length值，调整overlap以确保有效分段
    if max_length < 128:
        # 对于非常短的分段，减少重叠以避免分段过于相似
        overlap = min(overlap, max(10, int(max_length * 0.15)))
    
    # 分词并获取token IDs
    tokens = tokenizer.encode(text, add_special_tokens=False)
    
    # 如果token总数小于max_length，直接返回原文本
    if len(tokens) <= max_length:
        return [text]
    
    # 计算有效内容长度（减去特殊token）
    effective_length = max_length - 2  # 减去[CLS]和[SEP]
    
    # 计算步长（有效长度减去重叠部分）
    stride = effective_length - overlap
    
    # 确保步长至少为10
    stride = max(10, stride)
    
    # 分割token序列
    token_segments = []
    for i in range(0, len(tokens), stride):
        end = min(i + effective_length, len(tokens))
        token_segments.append(tokens[i:end])
        if end == len(tokens):
            break
    
    # 将token ID转回文本
    text_segments = []
    for segment in token_segments:
        segment_text = tokenizer.decode(segment)
        if len(segment_text.strip()) >= min_length * 0.5:  # 允许更短的段落
            text_segments.append(segment_text)
    
    # 确保至少返回一个段落
    if not text_segments and text.strip():
        text_segments = [text]
    
    return text_segments

def detect_text_segments_with_keywords(segments, tokenizer, model, threshold, keyword_detector, original_text=None):
    """检测文本分段（结合关键词检测）- 优化版"""
    segment_results = []
    nsfw_segments = 0
    total_segments = len(segments)

    # 首先对整个原始文档进行关键词检测（如果提供了原始文本）
    document_keyword_result = None
    document_keyword_priority = False

    if original_text and keyword_detector:
        document_keyword_result = keyword_detector.detect(original_text)
        document_keyword_priority = document_keyword_result.get("keyword_priority", False)

        # 如果整个文档的关键词检测有明确结果且优先级高，直接应用到所有分段
        if document_keyword_priority and document_keyword_result.get("final_decision"):
            document_decision = document_keyword_result["final_decision"]
            document_is_nsfw = document_decision == "NSFW"

            print(f"🔍 整个文档关键词检测结果: {document_decision}")
            print(f"📝 匹配的关键词: {document_keyword_result.get('matched_blacklist', [])} {document_keyword_result.get('matched_whitelist', [])}")

            # 为所有分段应用文档级别的关键词检测结果
            for i, segment in enumerate(segments):
                segment_result = {
                    "segment_index": i + 1,
                    "segment_text_full": segment,
                    "segment_text_preview": segment[:100] + "..." if len(segment) > 100 else segment,
                    "segment_length": len(segment),
                    "is_nsfw": document_is_nsfw,
                    "nsfw_probability": 1.0 if document_is_nsfw else 0.0,
                    "detection_method": "document_keyword",
                    "keyword_detection": document_keyword_result
                }

                segment_results.append(segment_result)

                if document_is_nsfw:
                    nsfw_segments += 1

            # 整体结果
            overall_result = {
                "total_segments": total_segments,
                "nsfw_segments": nsfw_segments,
                "nsfw_segment_ratio": round(nsfw_segments / total_segments, 4) if total_segments > 0 else 0,
                "overall_is_nsfw": document_is_nsfw,
                "overall_probability": 1.0 if document_is_nsfw else 0.0,
                "detection_strategy": "文档级关键词检测优先（整个文档匹配关键词）",
                "document_keyword_result": document_keyword_result
            }

            return segment_results, overall_result

    # 如果没有文档级关键词匹配，则进行分段检测
    print("🔍 进行分段级别的检测...")

    for i, segment in enumerate(segments):
        # 对每个分段进行关键词检测
        keyword_result = keyword_detector.detect(segment) if keyword_detector else {"keyword_priority": False}

        # 如果分段关键词检测有明确结果且优先级高，直接使用
        if keyword_result.get("keyword_priority", False) and keyword_result.get("final_decision"):
            is_nsfw = keyword_result["final_decision"] == "NSFW"
            prob = 1.0 if is_nsfw else 0.0
            detection_method = "segment_keyword"
            print(f"📍 分段 {i+1} 关键词匹配: {keyword_result['final_decision']}")
        else:
            # 使用模型检测
            inputs = tokenizer(segment, padding=True, truncation=True, max_length=MAX_LENGTH, return_tensors="pt")

            with torch.no_grad():
                outputs = model(**inputs)
                logits = outputs.logits
                probs = torch.softmax(logits, dim=-1)[:, 1].cpu().numpy()
                prob = probs[0]
                is_nsfw = bool(prob > threshold)
                detection_method = "model"

        segment_result = {
            "segment_index": i + 1,
            "segment_text_full": segment,
            "segment_text_preview": segment[:100] + "..." if len(segment) > 100 else segment,
            "segment_length": len(segment),
            "is_nsfw": is_nsfw,
            "nsfw_probability": float(round(prob, 4)),
            "detection_method": detection_method,
            "keyword_detection": keyword_result
        }

        segment_results.append(segment_result)

        if is_nsfw:
            nsfw_segments += 1

    # 整体判断策略：任何分段为NSFW则整体为NSFW
    overall_is_nsfw = nsfw_segments > 0
    overall_probability = max([r["nsfw_probability"] for r in segment_results]) if segment_results else 0.0

    overall_result = {
        "total_segments": total_segments,
        "nsfw_segments": nsfw_segments,
        "nsfw_segment_ratio": round(nsfw_segments / total_segments, 4) if total_segments > 0 else 0,
        "overall_is_nsfw": overall_is_nsfw,
        "overall_probability": overall_probability,
        "detection_strategy": "分段检测：任何分段为NSFW则整体为NSFW（关键词+模型联合检测）",
        "document_keyword_result": document_keyword_result
    }

    return segment_results, overall_result

def main():
    print("🚀 开始关键词+模型联合检测...")
    
    # 1. 加载关键词检测器
    keyword_config_file = os.path.join(BASE_MODEL_PATH, "keyword_config.json")
    keyword_detector = KeywordDetector(keyword_config_file)
    
    if keyword_detector.enabled:
        print("✅ 关键词检测已启用")
    else:
        print("⚠️  关键词检测已禁用，仅使用模型检测")
    
    # 2. 检查输入目录
    if not os.path.exists(TEST_DIR):
        print(f"❌ 输入目录不存在: {TEST_DIR}")
        return False
    
    txt_files = [f for f in os.listdir(TEST_DIR) if f.endswith('.txt')]
    if not txt_files:
        print(f"❌ 输入目录中没有txt文件: {TEST_DIR}")
        return False
    
    print(f"✅ 找到 {len(txt_files)} 个txt文件")
    
    # 3. 读取最优阈值
    param_file = os.path.join(BASE_MODEL_PATH, "auto_best_param.json")
    if not os.path.exists(param_file):
        print("💡 使用默认阈值 0.5")
        best_thr = 0.5
    else:
        with open(param_file, "r", encoding="utf-8") as f:
            params = json.load(f)
        best_thr = params.get("best_thr", 0.5)
        print(f"✅ 使用最优阈值: {best_thr}")
    
    # 4. 加载分词器和模型
    print("🔄 正在加载模型...")
    try:
        tokenizer = BertTokenizer.from_pretrained(BASE_MODEL_PATH)
        model = BertForSequenceClassification.from_pretrained(
            BASE_MODEL_PATH,
            torch_dtype=torch.float32,
            local_files_only=True,
        )
        model.eval()
        print("✅ 模型加载完成！")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False
    
    # 5. 处理文件
    print(f"🔍 开始关键词+模型联合检测 {len(txt_files)} 个文件...")
    
    results_list = []
    nsfw_count = 0
    sfw_count = 0
    keyword_hits = 0
    model_hits = 0
    
    for fname in tqdm(txt_files, desc="检测进度"):
        file_path = os.path.join(TEST_DIR, fname)
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read().strip()
        except Exception as e:
            print(f"⚠️  读取文件 {fname} 失败: {e}")
            continue
        
        if not content:
            print(f"⚠️  文件 {fname} 为空，跳过")
            continue
        
        # 首先对整个文档进行关键词检测
        doc_keyword_result = keyword_detector.detect(content)
        
        # 长文本分段检测
        segments = split_long_text(content, tokenizer)
        
        if len(segments) == 1:
            # 短文本处理
            if doc_keyword_result["keyword_priority"] and doc_keyword_result["final_decision"]:
                is_nsfw = doc_keyword_result["final_decision"] == "NSFW"
                prob = 1.0 if is_nsfw else 0.0
                detection_method = "keyword"
                keyword_hits += 1
            else:
                inputs = tokenizer(content, padding=True, truncation=True, max_length=MAX_LENGTH, return_tensors="pt")
                with torch.no_grad():
                    outputs = model(**inputs)
                    logits = outputs.logits
                    probs = torch.softmax(logits, dim=-1)[:, 1].cpu().numpy()
                    prob = probs[0]
                    is_nsfw = bool(prob > best_thr)
                    detection_method = "model"
                    model_hits += 1
            
            file_result = {
                "file_path": file_path,
                "file_name": fname,
                "is_nsfw": is_nsfw,
                "nsfw_probability": float(round(prob, 4)),
                "content_preview": content[:200] + "..." if len(content) > 200 else content,
                "content_full": content,  # 保存完整内容
                "detection_type": "single_segment",
                "detection_method": detection_method,
                "segment_count": 1,
                "document_keyword_result": doc_keyword_result
            }
            
            print(f"📄 {fname}: {'🔴 NSFW' if is_nsfw else '🟢 SFW'} ({prob:.4f}) [{detection_method}]")
        
        else:
            # 长文本，分段检测
            segment_results, overall_result = detect_text_segments_with_keywords(
                segments, tokenizer, model, best_thr, keyword_detector
            )
            
            is_nsfw = overall_result["overall_is_nsfw"]
            prob = overall_result["overall_probability"]
            
            # 统计检测方法
            for seg_result in segment_results:
                if seg_result["detection_method"] == "keyword":
                    keyword_hits += 1
                else:
                    model_hits += 1
            
            file_result = {
                "file_path": file_path,
                "file_name": fname,
                "is_nsfw": is_nsfw,
                "nsfw_probability": prob,
                "content_preview": content[:200] + "..." if len(content) > 200 else content,
                "content_full": content,  # 保存完整内容
                "detection_type": "multi_segment",
                "segment_count": overall_result["total_segments"],
                "nsfw_segments": overall_result["nsfw_segments"],
                "nsfw_segment_ratio": overall_result["nsfw_segment_ratio"],
                "segment_details": segment_results,
                "document_keyword_result": doc_keyword_result
            }
            
            print(f"📄 {fname}: {'🔴 NSFW' if is_nsfw else '🟢 SFW'} ({prob:.4f}) [{overall_result['total_segments']}段, {overall_result['nsfw_segments']}段NSFW]")
        
        results_list.append(file_result)
        
        if file_result["is_nsfw"]:
            nsfw_count += 1
        else:
            sfw_count += 1
    
    # 6. 保存结果
    total_files = len(results_list)
    summary = {
        "detection_time": now_str,
        "environment": "local_macos_keywords",
        "detection_method": "keyword_and_model_combined",
        "total_files": total_files,
        "nsfw_files": nsfw_count,
        "sfw_files": sfw_count,
        "nsfw_ratio": round(nsfw_count / total_files, 4) if total_files > 0 else 0,
        "detection_statistics": {
            "keyword_hits": keyword_hits,
            "model_hits": model_hits,
            "keyword_ratio": round(keyword_hits / (keyword_hits + model_hits), 4) if (keyword_hits + model_hits) > 0 else 0
        },
        "detection_config": {
            "threshold": best_thr,
            "max_length": MAX_LENGTH,
            "overlap_length": OVERLAP_LENGTH,
            "min_segment_length": MIN_SEGMENT_LENGTH,
            "keyword_enabled": keyword_detector.enabled,
            "keyword_config_file": keyword_config_file,
            "input_directory": TEST_DIR,
            "output_directory": RESULTS_DIR
        },
        "results": results_list
    }
    
    # 保存结果到JSON文件
    with open(RESULTS_JSON, "w", encoding="utf-8") as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print("\n" + "="*60)
    print("📊 关键词+模型联合检测完成！")
    print("="*60)
    print(f"📁 结果文件: {RESULTS_JSON}")
    print(f"📈 总文件数: {total_files}")
    print(f"🔴 NSFW文件: {nsfw_count}")
    print(f"🟢 SFW文件: {sfw_count}")
    print(f"📊 NSFW比例: {summary['nsfw_ratio']*100:.2f}%")
    print(f"🔑 关键词命中: {keyword_hits} 次")
    print(f"🤖 模型检测: {model_hits} 次")
    print(f"📊 关键词比例: {summary['detection_statistics']['keyword_ratio']*100:.2f}%")
    print("="*60)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 关键词+模型联合检测成功完成！")
    else:
        print("❌ 检测失败，请检查上述提示信息") 
