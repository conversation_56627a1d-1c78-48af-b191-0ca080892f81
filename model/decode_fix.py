#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复tokenizer decode的方法
"""

def decode_without_extra_spaces(tokenizer, tokens):
    """
    解码token而不添加多余空格
    适用于中文文本和WordPiece tokenizer
    """
    vocab = tokenizer.get_vocab()
    id_to_token = {v: k for k, v in vocab.items()}
    token_strs = [id_to_token[token_id] for token_id in tokens]
    
    # 处理WordPiece子词标记
    result = []
    for token in token_strs:
        if token.startswith('##'):
            # 这是一个子词，直接拼接到前一个token
            result.append(token[2:])  # 去掉##前缀
        else:
            result.append(token)
    
    return ''.join(result)

# 测试函数
if __name__ == "__main__":
    import sys
    sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model/bert_env/lib/python3.10/site-packages')
    from transformers import AutoTokenizer
    
    tokenizer = AutoTokenizer.from_pretrained('/root/semantic_analysis/sexual_content_dection/model/')
    
    test_cases = [
        '这是测试文本',
        '这是一个 包含空格的 文本',
        'english text',
        '中英混合mixed text',
    ]
    
    print("=== 修复后的解码方法测试 ===")
    for i, text in enumerate(test_cases, 1):
        tokens = tokenizer.encode(text, add_special_tokens=False)
        
        # 原始decode方法
        original_decoded = tokenizer.decode(tokens)
        
        # 修复后的解码方法
        fixed_decoded = decode_without_extra_spaces(tokenizer, tokens)
        
        print(f"案例{i}: {repr(text)}")
        print(f"  原始解码: {repr(original_decoded)}")
        print(f"  修复解码: {repr(fixed_decoded)}")
        print(f"  纯中文是否完全一致: {text == fixed_decoded}")
        print()
