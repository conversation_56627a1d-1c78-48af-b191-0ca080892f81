#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用tokenizer解码修复到batch_detect_with_keywords.py
"""

def apply_fix():
    # 读取当前文件
    with open('batch_detect_with_keywords.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加修复函数
    decode_fix_function = '''
def decode_without_extra_spaces(tokenizer, tokens):
    """
    解码token而不添加多余空格
    适用于中文文本和WordPiece tokenizer
    """
    vocab = tokenizer.get_vocab()
    id_to_token = {v: k for k, v in vocab.items()}
    token_strs = [id_to_token[token_id] for token_id in tokens]
    
    # 处理WordPiece子词标记
    result = []
    for token in token_strs:
        if token.startswith('##'):
            # 这是一个子词，直接拼接到前一个token
            result.append(token[2:])  # 去掉##前缀
        else:
            result.append(token)
    
    return ''.join(result)
'''
    
    # 在imports后面添加修复函数
    import_end = content.find('\n\n', content.find('import'))
    if import_end == -1:
        import_end = content.find('\n\nclass')
    
    if import_end != -1:
        content = content[:import_end] + decode_fix_function + content[import_end:]
    
    # 替换tokenizer.decode调用
    old_line = '        segment_text = tokenizer.decode(segment)'
    new_line = '        segment_text = decode_without_extra_spaces(tokenizer, segment)'
    
    content = content.replace(old_line, new_line)
    
    # 写回文件
    with open('batch_detect_with_keywords.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("修复已应用到 batch_detect_with_keywords.py")
    print("主要变更:")
    print("1. 添加了 decode_without_extra_spaces 函数")
    print("2. 替换了 tokenizer.decode(segment) 调用")

if __name__ == "__main__":
    apply_fix()
