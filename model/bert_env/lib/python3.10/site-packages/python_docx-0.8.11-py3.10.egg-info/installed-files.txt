../docx/__init__.py
../docx/__pycache__/__init__.cpython-310.pyc
../docx/__pycache__/api.cpython-310.pyc
../docx/__pycache__/blkcntnr.cpython-310.pyc
../docx/__pycache__/compat.cpython-310.pyc
../docx/__pycache__/document.cpython-310.pyc
../docx/__pycache__/exceptions.cpython-310.pyc
../docx/__pycache__/package.cpython-310.pyc
../docx/__pycache__/section.cpython-310.pyc
../docx/__pycache__/settings.cpython-310.pyc
../docx/__pycache__/shape.cpython-310.pyc
../docx/__pycache__/shared.cpython-310.pyc
../docx/__pycache__/table.cpython-310.pyc
../docx/api.py
../docx/blkcntnr.py
../docx/compat.py
../docx/dml/__init__.py
../docx/dml/__pycache__/__init__.cpython-310.pyc
../docx/dml/__pycache__/color.cpython-310.pyc
../docx/dml/color.py
../docx/document.py
../docx/enum/__init__.py
../docx/enum/__pycache__/__init__.cpython-310.pyc
../docx/enum/__pycache__/base.cpython-310.pyc
../docx/enum/__pycache__/dml.cpython-310.pyc
../docx/enum/__pycache__/section.cpython-310.pyc
../docx/enum/__pycache__/shape.cpython-310.pyc
../docx/enum/__pycache__/style.cpython-310.pyc
../docx/enum/__pycache__/table.cpython-310.pyc
../docx/enum/__pycache__/text.cpython-310.pyc
../docx/enum/base.py
../docx/enum/dml.py
../docx/enum/section.py
../docx/enum/shape.py
../docx/enum/style.py
../docx/enum/table.py
../docx/enum/text.py
../docx/exceptions.py
../docx/image/__init__.py
../docx/image/__pycache__/__init__.cpython-310.pyc
../docx/image/__pycache__/bmp.cpython-310.pyc
../docx/image/__pycache__/constants.cpython-310.pyc
../docx/image/__pycache__/exceptions.cpython-310.pyc
../docx/image/__pycache__/gif.cpython-310.pyc
../docx/image/__pycache__/helpers.cpython-310.pyc
../docx/image/__pycache__/image.cpython-310.pyc
../docx/image/__pycache__/jpeg.cpython-310.pyc
../docx/image/__pycache__/png.cpython-310.pyc
../docx/image/__pycache__/tiff.cpython-310.pyc
../docx/image/bmp.py
../docx/image/constants.py
../docx/image/exceptions.py
../docx/image/gif.py
../docx/image/helpers.py
../docx/image/image.py
../docx/image/jpeg.py
../docx/image/png.py
../docx/image/tiff.py
../docx/opc/__init__.py
../docx/opc/__pycache__/__init__.cpython-310.pyc
../docx/opc/__pycache__/compat.cpython-310.pyc
../docx/opc/__pycache__/constants.cpython-310.pyc
../docx/opc/__pycache__/coreprops.cpython-310.pyc
../docx/opc/__pycache__/exceptions.cpython-310.pyc
../docx/opc/__pycache__/oxml.cpython-310.pyc
../docx/opc/__pycache__/package.cpython-310.pyc
../docx/opc/__pycache__/packuri.cpython-310.pyc
../docx/opc/__pycache__/part.cpython-310.pyc
../docx/opc/__pycache__/phys_pkg.cpython-310.pyc
../docx/opc/__pycache__/pkgreader.cpython-310.pyc
../docx/opc/__pycache__/pkgwriter.cpython-310.pyc
../docx/opc/__pycache__/rel.cpython-310.pyc
../docx/opc/__pycache__/shared.cpython-310.pyc
../docx/opc/__pycache__/spec.cpython-310.pyc
../docx/opc/compat.py
../docx/opc/constants.py
../docx/opc/coreprops.py
../docx/opc/exceptions.py
../docx/opc/oxml.py
../docx/opc/package.py
../docx/opc/packuri.py
../docx/opc/part.py
../docx/opc/parts/__init__.py
../docx/opc/parts/__pycache__/__init__.cpython-310.pyc
../docx/opc/parts/__pycache__/coreprops.cpython-310.pyc
../docx/opc/parts/coreprops.py
../docx/opc/phys_pkg.py
../docx/opc/pkgreader.py
../docx/opc/pkgwriter.py
../docx/opc/rel.py
../docx/opc/shared.py
../docx/opc/spec.py
../docx/oxml/__init__.py
../docx/oxml/__pycache__/__init__.cpython-310.pyc
../docx/oxml/__pycache__/coreprops.cpython-310.pyc
../docx/oxml/__pycache__/document.cpython-310.pyc
../docx/oxml/__pycache__/exceptions.cpython-310.pyc
../docx/oxml/__pycache__/ns.cpython-310.pyc
../docx/oxml/__pycache__/numbering.cpython-310.pyc
../docx/oxml/__pycache__/section.cpython-310.pyc
../docx/oxml/__pycache__/settings.cpython-310.pyc
../docx/oxml/__pycache__/shape.cpython-310.pyc
../docx/oxml/__pycache__/shared.cpython-310.pyc
../docx/oxml/__pycache__/simpletypes.cpython-310.pyc
../docx/oxml/__pycache__/styles.cpython-310.pyc
../docx/oxml/__pycache__/table.cpython-310.pyc
../docx/oxml/__pycache__/xmlchemy.cpython-310.pyc
../docx/oxml/coreprops.py
../docx/oxml/document.py
../docx/oxml/exceptions.py
../docx/oxml/ns.py
../docx/oxml/numbering.py
../docx/oxml/section.py
../docx/oxml/settings.py
../docx/oxml/shape.py
../docx/oxml/shared.py
../docx/oxml/simpletypes.py
../docx/oxml/styles.py
../docx/oxml/table.py
../docx/oxml/text/__init__.py
../docx/oxml/text/__pycache__/__init__.cpython-310.pyc
../docx/oxml/text/__pycache__/font.cpython-310.pyc
../docx/oxml/text/__pycache__/paragraph.cpython-310.pyc
../docx/oxml/text/__pycache__/parfmt.cpython-310.pyc
../docx/oxml/text/__pycache__/run.cpython-310.pyc
../docx/oxml/text/font.py
../docx/oxml/text/paragraph.py
../docx/oxml/text/parfmt.py
../docx/oxml/text/run.py
../docx/oxml/xmlchemy.py
../docx/package.py
../docx/parts/__init__.py
../docx/parts/__pycache__/__init__.cpython-310.pyc
../docx/parts/__pycache__/document.cpython-310.pyc
../docx/parts/__pycache__/hdrftr.cpython-310.pyc
../docx/parts/__pycache__/image.cpython-310.pyc
../docx/parts/__pycache__/numbering.cpython-310.pyc
../docx/parts/__pycache__/settings.cpython-310.pyc
../docx/parts/__pycache__/story.cpython-310.pyc
../docx/parts/__pycache__/styles.cpython-310.pyc
../docx/parts/document.py
../docx/parts/hdrftr.py
../docx/parts/image.py
../docx/parts/numbering.py
../docx/parts/settings.py
../docx/parts/story.py
../docx/parts/styles.py
../docx/section.py
../docx/settings.py
../docx/shape.py
../docx/shared.py
../docx/styles/__init__.py
../docx/styles/__pycache__/__init__.cpython-310.pyc
../docx/styles/__pycache__/latent.cpython-310.pyc
../docx/styles/__pycache__/style.cpython-310.pyc
../docx/styles/__pycache__/styles.cpython-310.pyc
../docx/styles/latent.py
../docx/styles/style.py
../docx/styles/styles.py
../docx/table.py
../docx/templates/default-footer.xml
../docx/templates/default-header.xml
../docx/templates/default-settings.xml
../docx/templates/default-styles.xml
../docx/templates/default.docx
../docx/text/__init__.py
../docx/text/__pycache__/__init__.cpython-310.pyc
../docx/text/__pycache__/font.cpython-310.pyc
../docx/text/__pycache__/paragraph.cpython-310.pyc
../docx/text/__pycache__/parfmt.cpython-310.pyc
../docx/text/__pycache__/run.cpython-310.pyc
../docx/text/__pycache__/tabstops.cpython-310.pyc
../docx/text/font.py
../docx/text/paragraph.py
../docx/text/parfmt.py
../docx/text/run.py
../docx/text/tabstops.py
PKG-INFO
SOURCES.txt
dependency_links.txt
not-zip-safe
requires.txt
top_level.txt
