HISTORY.rst
LICENSE
MANIFEST.in
README.rst
setup.cfg
setup.py
tox.ini
docs/Makefile
docs/Session.vim
docs/conf.py
docs/index.rst
docs/_static/.gitignore
docs/_static/img/example-docx-01.png
docs/_static/img/hdrftr-01.png
docs/_static/img/hdrftr-02.png
docs/_templates/sidebarlinks.html
docs/_themes/armstrong/LICENSE
docs/_themes/armstrong/layout.html
docs/_themes/armstrong/rtd-themes.conf
docs/_themes/armstrong/theme.conf
docs/_themes/armstrong/theme.conf.orig
docs/_themes/armstrong/static/rtd.css_t
docs/api/dml.rst
docs/api/document.rst
docs/api/section.rst
docs/api/settings.rst
docs/api/shape.rst
docs/api/shared.rst
docs/api/style.rst
docs/api/table.rst
docs/api/text.rst
docs/api/enum/MsoColorType.rst
docs/api/enum/MsoThemeColorIndex.rst
docs/api/enum/WdAlignParagraph.rst
docs/api/enum/WdBuiltinStyle.rst
docs/api/enum/WdCellVerticalAlignment.rst
docs/api/enum/WdColorIndex.rst
docs/api/enum/WdLineSpacing.rst
docs/api/enum/WdOrientation.rst
docs/api/enum/WdRowAlignment.rst
docs/api/enum/WdRowHeightRule.rst
docs/api/enum/WdSectionStart.rst
docs/api/enum/WdStyleType.rst
docs/api/enum/WdTabAlignment.rst
docs/api/enum/WdTabLeader.rst
docs/api/enum/WdTableDirection.rst
docs/api/enum/WdUnderline.rst
docs/api/enum/index.rst
docs/dev/analysis/index.rst
docs/dev/analysis/features/coreprops.rst
docs/dev/analysis/features/header.rst
docs/dev/analysis/features/numbering.rst
docs/dev/analysis/features/sections.rst
docs/dev/analysis/features/settings.rst
docs/dev/analysis/features/shapes/index.rst
docs/dev/analysis/features/shapes/picture.rst
docs/dev/analysis/features/shapes/shapes-inline-size.rst
docs/dev/analysis/features/shapes/shapes-inline.rst
docs/dev/analysis/features/styles/character-style.rst
docs/dev/analysis/features/styles/index.rst
docs/dev/analysis/features/styles/latent-styles.rst
docs/dev/analysis/features/styles/paragraph-style.rst
docs/dev/analysis/features/styles/style.rst
docs/dev/analysis/features/styles/styles.rst
docs/dev/analysis/features/table/cell-merge.rst
docs/dev/analysis/features/table/index.rst
docs/dev/analysis/features/table/table-cell.rst
docs/dev/analysis/features/table/table-props.rst
docs/dev/analysis/features/table/table-row.rst
docs/dev/analysis/features/text/breaks.rst
docs/dev/analysis/features/text/font-color.rst
docs/dev/analysis/features/text/font-highlight-color.rst
docs/dev/analysis/features/text/font.rst
docs/dev/analysis/features/text/index.rst
docs/dev/analysis/features/text/paragraph-format.rst
docs/dev/analysis/features/text/run-content.rst
docs/dev/analysis/features/text/tab-stops.rst
docs/dev/analysis/features/text/underline.rst
docs/dev/analysis/schema/ct_body.rst
docs/dev/analysis/schema/ct_document.rst
docs/dev/analysis/schema/ct_p.rst
docs/user/api-concepts.rst
docs/user/documents.rst
docs/user/hdrftr.rst
docs/user/install.rst
docs/user/quickstart.rst
docs/user/sections.rst
docs/user/shapes.rst
docs/user/styles-understanding.rst
docs/user/styles-using.rst
docs/user/text.rst
docx/__init__.py
docx/api.py
docx/blkcntnr.py
docx/compat.py
docx/document.py
docx/exceptions.py
docx/package.py
docx/section.py
docx/settings.py
docx/shape.py
docx/shared.py
docx/table.py
docx/dml/__init__.py
docx/dml/color.py
docx/enum/__init__.py
docx/enum/base.py
docx/enum/dml.py
docx/enum/section.py
docx/enum/shape.py
docx/enum/style.py
docx/enum/table.py
docx/enum/text.py
docx/image/__init__.py
docx/image/bmp.py
docx/image/constants.py
docx/image/exceptions.py
docx/image/gif.py
docx/image/helpers.py
docx/image/image.py
docx/image/jpeg.py
docx/image/png.py
docx/image/tiff.py
docx/opc/__init__.py
docx/opc/compat.py
docx/opc/constants.py
docx/opc/coreprops.py
docx/opc/exceptions.py
docx/opc/oxml.py
docx/opc/package.py
docx/opc/packuri.py
docx/opc/part.py
docx/opc/phys_pkg.py
docx/opc/pkgreader.py
docx/opc/pkgwriter.py
docx/opc/rel.py
docx/opc/shared.py
docx/opc/spec.py
docx/opc/parts/__init__.py
docx/opc/parts/coreprops.py
docx/oxml/__init__.py
docx/oxml/coreprops.py
docx/oxml/document.py
docx/oxml/exceptions.py
docx/oxml/ns.py
docx/oxml/numbering.py
docx/oxml/section.py
docx/oxml/settings.py
docx/oxml/shape.py
docx/oxml/shared.py
docx/oxml/simpletypes.py
docx/oxml/styles.py
docx/oxml/table.py
docx/oxml/xmlchemy.py
docx/oxml/text/__init__.py
docx/oxml/text/font.py
docx/oxml/text/paragraph.py
docx/oxml/text/parfmt.py
docx/oxml/text/run.py
docx/parts/__init__.py
docx/parts/document.py
docx/parts/hdrftr.py
docx/parts/image.py
docx/parts/numbering.py
docx/parts/settings.py
docx/parts/story.py
docx/parts/styles.py
docx/styles/__init__.py
docx/styles/latent.py
docx/styles/style.py
docx/styles/styles.py
docx/templates/default-footer.xml
docx/templates/default-header.xml
docx/templates/default-settings.xml
docx/templates/default-styles.xml
docx/templates/default.docx
docx/templates/default-docx-template/[Content_Types].xml
docx/templates/default-docx-template/_rels/.rels
docx/templates/default-docx-template/customXml/item1.xml
docx/templates/default-docx-template/customXml/itemProps1.xml
docx/templates/default-docx-template/customXml/_rels/item1.xml.rels
docx/templates/default-docx-template/docProps/app.xml
docx/templates/default-docx-template/docProps/core.xml
docx/templates/default-docx-template/docProps/thumbnail.jpeg
docx/templates/default-docx-template/word/document.xml
docx/templates/default-docx-template/word/fontTable.xml
docx/templates/default-docx-template/word/numbering.xml
docx/templates/default-docx-template/word/settings.xml
docx/templates/default-docx-template/word/styles.xml
docx/templates/default-docx-template/word/stylesWithEffects.xml
docx/templates/default-docx-template/word/webSettings.xml
docx/templates/default-docx-template/word/_rels/document.xml.rels
docx/templates/default-docx-template/word/theme/theme1.xml
docx/text/__init__.py
docx/text/font.py
docx/text/paragraph.py
docx/text/parfmt.py
docx/text/run.py
docx/text/tabstops.py
features/api-open-document.feature
features/blk-add-paragraph.feature
features/blk-add-table.feature
features/cel-add-table.feature
features/cel-text.feature
features/doc-access-collections.feature
features/doc-access-sections.feature
features/doc-add-heading.feature
features/doc-add-page-break.feature
features/doc-add-paragraph.feature
features/doc-add-picture.feature
features/doc-add-section.feature
features/doc-add-table.feature
features/doc-coreprops.feature
features/doc-settings.feature
features/doc-styles.feature
features/environment.py
features/hdr-header-footer.feature
features/img-characterize-image.feature
features/num-access-numbering-part.feature
features/par-access-parfmt.feature
features/par-add-run.feature
features/par-alignment-prop.feature
features/par-clear-paragraph.feature
features/par-insert-paragraph.feature
features/par-set-text.feature
features/par-style-prop.feature
features/run-access-content.feature
features/run-access-font.feature
features/run-add-content.feature
features/run-add-picture.feature
features/run-char-style.feature
features/run-clear-run.feature
features/run-enum-props.feature
features/sct-section.feature
features/shp-inline-shape-access.feature
features/shp-inline-shape-size.feature
features/sty-access-font.feature
features/sty-access-latent-styles.feature
features/sty-access-parfmt.feature
features/sty-add-style.feature
features/sty-delete-style.feature
features/sty-latent-add-del.feature
features/sty-latent-props.feature
features/sty-style-props.feature
features/tab-access-tabs.feature
features/tab-tabstop-props.feature
features/tbl-add-row-or-col.feature
features/tbl-cell-access.feature
features/tbl-cell-props.feature
features/tbl-col-props.feature
features/tbl-item-access.feature
features/tbl-merge-cells.feature
features/tbl-props.feature
features/tbl-row-props.feature
features/tbl-style.feature
features/txt-add-break.feature
features/txt-font-color.feature
features/txt-font-props.feature
features/txt-parfmt-props.feature
features/_scratch/test_out.docx
features/_scratch/test_out/[Content_Types].xml
features/_scratch/test_out/_rels/.rels
features/_scratch/test_out/customXml/item1.xml
features/_scratch/test_out/customXml/itemProps1.xml
features/_scratch/test_out/customXml/_rels/item1.xml.rels
features/_scratch/test_out/docProps/app.xml
features/_scratch/test_out/docProps/core.xml
features/_scratch/test_out/docProps/thumbnail.jpeg
features/_scratch/test_out/word/document.xml
features/_scratch/test_out/word/fontTable.xml
features/_scratch/test_out/word/numbering.xml
features/_scratch/test_out/word/settings.xml
features/_scratch/test_out/word/styles.xml
features/_scratch/test_out/word/stylesWithEffects.xml
features/_scratch/test_out/word/webSettings.xml
features/_scratch/test_out/word/_rels/document.xml.rels
features/_scratch/test_out/word/theme/theme1.xml
features/steps/api.py
features/steps/block.py
features/steps/cell.py
features/steps/coreprops.py
features/steps/document.py
features/steps/font.py
features/steps/hdrftr.py
features/steps/helpers.py
features/steps/image.py
features/steps/numbering.py
features/steps/paragraph.py
features/steps/parfmt.py
features/steps/section.py
features/steps/settings.py
features/steps/shape.py
features/steps/shared.py
features/steps/styles.py
features/steps/table.py
features/steps/tabstops.py
features/steps/text.py
features/steps/test_files/blk-containing-table.docx
features/steps/test_files/court-exif.jpg
features/steps/test_files/doc-access-sections.docx
features/steps/test_files/doc-add-section.docx
features/steps/test_files/doc-coreprops.docx
features/steps/test_files/doc-default.docx
features/steps/test_files/doc-no-coreprops.docx
features/steps/test_files/doc-odd-even-hdrs.docx
features/steps/test_files/doc-word-default-blank.docx
features/steps/test_files/fnt-color.docx
features/steps/test_files/hdr-header-footer.docx
features/steps/test_files/jfif-300-dpi.jpg
features/steps/test_files/jpeg420exif.jpg
features/steps/test_files/lena.bmp
features/steps/test_files/lena.gif
features/steps/test_files/lena.tif
features/steps/test_files/lena_std.jpg
features/steps/test_files/monty-truth.png
features/steps/test_files/mountain.bmp
features/steps/test_files/num-having-numbering-part.docx
features/steps/test_files/par-alignment.docx
features/steps/test_files/par-known-paragraphs.docx
features/steps/test_files/par-known-styles.docx
features/steps/test_files/python-icon.jpeg
features/steps/test_files/run-char-style.docx
features/steps/test_files/run-enumerated-props.docx
features/steps/test_files/sample.tif
features/steps/test_files/sct-first-page-hdrftr.docx
features/steps/test_files/sct-section-props.docx
features/steps/test_files/set-no-settings-part.docx
features/steps/test_files/shp-inline-shape-access.docx
features/steps/test_files/sty-behav-props.docx
features/steps/test_files/sty-having-no-styles-part.docx
features/steps/test_files/sty-having-styles-part.docx
features/steps/test_files/sty-known-styles.docx
features/steps/test_files/tab-stops.docx
features/steps/test_files/tbl-2x2-table.docx
features/steps/test_files/tbl-cell-access.docx
features/steps/test_files/tbl-col-props.docx
features/steps/test_files/tbl-having-applied-style.docx
features/steps/test_files/tbl-having-tables.docx
features/steps/test_files/tbl-on-off-props.docx
features/steps/test_files/tbl-props.docx
features/steps/test_files/test.png
features/steps/test_files/txt-font-highlight-color.docx
features/steps/test_files/txt-font-props.docx
python_docx.egg-info/PKG-INFO
python_docx.egg-info/SOURCES.txt
python_docx.egg-info/dependency_links.txt
python_docx.egg-info/not-zip-safe
python_docx.egg-info/requires.txt
python_docx.egg-info/top_level.txt
tests/__init__.py
tests/test_api.py
tests/test_blkcntnr.py
tests/test_document.py
tests/test_enum.py
tests/test_package.py
tests/test_section.py
tests/test_settings.py
tests/test_shape.py
tests/test_shared.py
tests/test_table.py
tests/unitdata.py
tests/dml/__init__.py
tests/dml/test_color.py
tests/image/__init__.py
tests/image/test_bmp.py
tests/image/test_gif.py
tests/image/test_helpers.py
tests/image/test_image.py
tests/image/test_jpeg.py
tests/image/test_png.py
tests/image/test_tiff.py
tests/opc/__init__.py
tests/opc/test_coreprops.py
tests/opc/test_oxml.py
tests/opc/test_package.py
tests/opc/test_packuri.py
tests/opc/test_part.py
tests/opc/test_phys_pkg.py
tests/opc/test_pkgreader.py
tests/opc/test_pkgwriter.py
tests/opc/test_rel.py
tests/opc/parts/__init__.py
tests/opc/parts/test_coreprops.py
tests/opc/unitdata/__init__.py
tests/opc/unitdata/rels.py
tests/opc/unitdata/types.py
tests/oxml/__init__.py
tests/oxml/test__init__.py
tests/oxml/test_ns.py
tests/oxml/test_styles.py
tests/oxml/test_table.py
tests/oxml/test_xmlchemy.py
tests/oxml/parts/__init__.py
tests/oxml/parts/test_document.py
tests/oxml/parts/unitdata/__init__.py
tests/oxml/parts/unitdata/document.py
tests/oxml/text/__init__.py
tests/oxml/text/test_run.py
tests/oxml/unitdata/__init__.py
tests/oxml/unitdata/dml.py
tests/oxml/unitdata/numbering.py
tests/oxml/unitdata/section.py
tests/oxml/unitdata/shared.py
tests/oxml/unitdata/styles.py
tests/oxml/unitdata/table.py
tests/oxml/unitdata/text.py
tests/parts/__init__.py
tests/parts/test_document.py
tests/parts/test_hdrftr.py
tests/parts/test_image.py
tests/parts/test_numbering.py
tests/parts/test_settings.py
tests/parts/test_story.py
tests/parts/test_styles.py
tests/styles/__init__.py
tests/styles/test_latent.py
tests/styles/test_style.py
tests/styles/test_styles.py
tests/test_files/150-dpi.png
tests/test_files/300-dpi.TIF
tests/test_files/300-dpi.jpg
tests/test_files/300-dpi.png
tests/test_files/72-dpi.tiff
tests/test_files/CVS_LOGO.WMF
tests/test_files/exif-420-dpi.jpg
tests/test_files/having-images.docx
tests/test_files/jfif-iguana.jpg
tests/test_files/little-endian.tif
tests/test_files/monty-truth.png
tests/test_files/python-icon.jpeg
tests/test_files/python-icon.png
tests/test_files/python-powered.png
tests/test_files/python.bmp
tests/test_files/sonic.gif
tests/test_files/test.docx
tests/test_files/expanded_docx/[Content_Types].xml
tests/test_files/expanded_docx/_rels/.rels
tests/test_files/expanded_docx/customXml/item1.xml
tests/test_files/expanded_docx/customXml/itemProps1.xml
tests/test_files/expanded_docx/customXml/_rels/item1.xml.rels
tests/test_files/expanded_docx/docProps/app.xml
tests/test_files/expanded_docx/docProps/core.xml
tests/test_files/expanded_docx/docProps/thumbnail.jpeg
tests/test_files/expanded_docx/word/document.xml
tests/test_files/expanded_docx/word/fontTable.xml
tests/test_files/expanded_docx/word/numbering.xml
tests/test_files/expanded_docx/word/settings.xml
tests/test_files/expanded_docx/word/styles.xml
tests/test_files/expanded_docx/word/stylesWithEffects.xml
tests/test_files/expanded_docx/word/webSettings.xml
tests/test_files/expanded_docx/word/_rels/document.xml.rels
tests/test_files/expanded_docx/word/theme/theme1.xml
tests/test_files/snippets/add-row-col.txt
tests/test_files/snippets/inline.txt
tests/test_files/snippets/new-tbl.txt
tests/test_files/snippets/tbl-cells.txt
tests/text/__init__.py
tests/text/test_font.py
tests/text/test_paragraph.py
tests/text/test_parfmt.py
tests/text/test_run.py
tests/text/test_tabstops.py
tests/unitutil/__init__.py
tests/unitutil/cxml.py
tests/unitutil/file.py
tests/unitutil/mock.py