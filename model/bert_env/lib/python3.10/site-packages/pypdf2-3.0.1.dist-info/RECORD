PyPDF2/__init__.py,sha256=L8aP6Tz9KflekpLy0IiO6OfpK1Y6vszAr9jRj2Z4co0,1338
PyPDF2/__pycache__/__init__.cpython-310.pyc,,
PyPDF2/__pycache__/_cmap.cpython-310.pyc,,
PyPDF2/__pycache__/_encryption.cpython-310.pyc,,
PyPDF2/__pycache__/_merger.cpython-310.pyc,,
PyPDF2/__pycache__/_page.cpython-310.pyc,,
PyPDF2/__pycache__/_protocols.cpython-310.pyc,,
PyPDF2/__pycache__/_reader.cpython-310.pyc,,
PyPDF2/__pycache__/_security.cpython-310.pyc,,
PyPDF2/__pycache__/_utils.cpython-310.pyc,,
PyPDF2/__pycache__/_version.cpython-310.pyc,,
PyPDF2/__pycache__/_writer.cpython-310.pyc,,
PyPDF2/__pycache__/constants.cpython-310.pyc,,
PyPDF2/__pycache__/errors.cpython-310.pyc,,
PyPDF2/__pycache__/filters.cpython-310.pyc,,
PyPDF2/__pycache__/pagerange.cpython-310.pyc,,
PyPDF2/__pycache__/papersizes.cpython-310.pyc,,
PyPDF2/__pycache__/types.cpython-310.pyc,,
PyPDF2/__pycache__/xmp.cpython-310.pyc,,
PyPDF2/_cmap.py,sha256=nwGfthg7CJF7CXVWTa5_BGMhGloJugV3Tn6hd81m6u4,14645
PyPDF2/_codecs/__init__.py,sha256=y4x5s4q00SlzSjNkDDqaI38uDO0L5IpyXyOKgtFgZ1E,1720
PyPDF2/_codecs/__pycache__/__init__.cpython-310.pyc,,
PyPDF2/_codecs/__pycache__/adobe_glyphs.cpython-310.pyc,,
PyPDF2/_codecs/__pycache__/pdfdoc.cpython-310.pyc,,
PyPDF2/_codecs/__pycache__/std.cpython-310.pyc,,
PyPDF2/_codecs/__pycache__/symbol.cpython-310.pyc,,
PyPDF2/_codecs/__pycache__/zapfding.cpython-310.pyc,,
PyPDF2/_codecs/adobe_glyphs.py,sha256=aMXhp5va7TgNyHEmnS9ZqA3G-h8Te8Ew7p7kCsgKJLY,431492
PyPDF2/_codecs/pdfdoc.py,sha256=xfSvMFYsvxuaSQ0Uu9vZDKaB0Wu85h1uCiB1i9rAcUU,4269
PyPDF2/_codecs/std.py,sha256=DyQMuEpAGEpS9uy1jWf4cnj-kqShPOAij5sI7Q1YD8E,2630
PyPDF2/_codecs/symbol.py,sha256=nIaGQIlhWCJiPMHrwUlmGHH-_fOXyEKvguRmuKXcGAk,3734
PyPDF2/_codecs/zapfding.py,sha256=PQxjxRC616d41xF3exVxP1W8nM4QrZfjO3lmtLxpE_s,3742
PyPDF2/_encryption.py,sha256=KaaIKpGzG921muzE-FkQB2SnKuDhoioFkDL7t9yvPOw,38979
PyPDF2/_merger.py,sha256=-hskprroJsRC9gvUmNcGzx34Qu_m5qOm3UytZCIYwYg,30464
PyPDF2/_page.py,sha256=su41KemcbUSE6oWYd1HYvWHEkuzuBKHh6BxnlJcshX4,82079
PyPDF2/_protocols.py,sha256=7Y-5QbYVRBrWJmWv536jgX_XUPODrh25IKXaeY9tuEI,1486
PyPDF2/_reader.py,sha256=i53XyAVa5N9UYDAkny6NdfMvrsVawJabRhUA3pN5oVI,77206
PyPDF2/_security.py,sha256=rwJUT1_W46c1pQshRH22Q5P5Gb_3_LorMvV9xnXNST0,10628
PyPDF2/_utils.py,sha256=93acqHLpvbBuIo57Ot_OHQ5MeAt-1HABWwijjbPA7RI,14252
PyPDF2/_version.py,sha256=E3P6AbnCwaWk6ndR1zNqlOTVebX9z5rv9voltc71dos,22
PyPDF2/_writer.py,sha256=Sx7Ctf8pIBDVgxlKjyZuDfCw9lvSXCBf7a4WF-JFC88,106551
PyPDF2/constants.py,sha256=2O1gjddmSZGv8XMpUMKI09MBL0gIBBkxZBNFx3DXIFY,13154
PyPDF2/errors.py,sha256=BZ7z1dFjppXNvWilX8BoVCEGFxCapXas05JWs0XsNrc,782
PyPDF2/filters.py,sha256=d0h5rpehpBZh2i1yqQSJGchDUTKuzUvjVGfm2hmTzfk,24364
PyPDF2/generic/__init__.py,sha256=YXnX-pSPDwSUPv6CQSG76Bxhq3Q-B3kpXa3bE_BxnWU,4413
PyPDF2/generic/__pycache__/__init__.cpython-310.pyc,,
PyPDF2/generic/__pycache__/_annotations.cpython-310.pyc,,
PyPDF2/generic/__pycache__/_base.cpython-310.pyc,,
PyPDF2/generic/__pycache__/_data_structures.cpython-310.pyc,,
PyPDF2/generic/__pycache__/_fit.cpython-310.pyc,,
PyPDF2/generic/__pycache__/_outline.cpython-310.pyc,,
PyPDF2/generic/__pycache__/_rectangle.cpython-310.pyc,,
PyPDF2/generic/__pycache__/_utils.cpython-310.pyc,,
PyPDF2/generic/_annotations.py,sha256=bjwsoFWxQWTmkzLO8gwekHDMbH8xlOB08RLm96E9jJI,10065
PyPDF2/generic/_base.py,sha256=fdQOICdpzzPlQ2h_fBWJ631dTvYCqm1qPRLQyc9el8o,23986
PyPDF2/generic/_data_structures.py,sha256=cENL4Z3FFeaLccTywz6sIBXDdeMdny5v8GRcyvK5M_o,51408
PyPDF2/generic/_fit.py,sha256=sUoDxD_y_Jz5y6V7-IcCRHg961096oXqFXt1MtOGE5w,4894
PyPDF2/generic/_outline.py,sha256=7d2eaAqoPRSh9RVZxbPJNwwnauyA0MKh0cGMxln9NTc,1201
PyPDF2/generic/_rectangle.py,sha256=yyGkaXj7S2ShBAUR23OJkXDf1zTR2QA-207Vcuy34bg,9439
PyPDF2/generic/_utils.py,sha256=NMEwhDNbUHSjIJ39OTtiMNtMWwm0vDFbs6NTGw12DS8,6272
PyPDF2/pagerange.py,sha256=PuVME9JOTFN6UbegjU_a0Q2Tvi2Pvgaw84yhT1ik5Eo,6415
PyPDF2/papersizes.py,sha256=p82oLUyKE4dyCkID5NmsnNX2N-1d8u64qYmcCJ-G2nM,1369
PyPDF2/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
PyPDF2/types.py,sha256=aZt-WmZ3QQNVSI-R1_GbFy0rApINHqsV-fyUwmyJ0SU,1676
PyPDF2/xmp.py,sha256=CCN838ewDipjDgBLtj256a4ZPgSJxMyD9lOOQdEbhD0,18354
pypdf2-3.0.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pypdf2-3.0.1.dist-info/LICENSE,sha256=qXrCMOXzPvEKU2eoUOsB-R8aCwZONHQsd5TSKUVX9SQ,1605
pypdf2-3.0.1.dist-info/METADATA,sha256=TqZRd2BwsQotikOd2PwuBpwcdD7F8ukmMNkndi0ePDM,6805
pypdf2-3.0.1.dist-info/RECORD,,
pypdf2-3.0.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf2-3.0.1.dist-info/WHEEL,sha256=rSgq_JpHF9fHR1lx53qwg_1-2LypZE_qmcuXbVUq948,81
