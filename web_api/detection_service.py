#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测服务模块
集成现有的BERT模型和关键词检测功能
"""

import os
import sys
import json
import torch
import uuid
import threading
import time
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import multiprocessing
from transformers import BertTokenizer, BertForSequenceClassification

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入现有的检测模块
from model.batch_detect_with_keywords import KeywordDetector, split_long_text, detect_text_segments_with_keywords

class DetectionService:
    """检测服务类"""
    
    def __init__(self):
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.model_path = os.path.join(self.base_dir, "model")
        self.config_path = os.path.join(self.model_path, "keyword_config.json")
        
        # 模型配置 - 支持动态调整
        self.threshold = 0.85  # 默认阈值
        self.threshold_modes = {
            'strict': 0.75,      # 严格模式 - 更容易判定为NSFW
            'balanced': 0.85,    # 平衡模式 - 默认值
            'lenient': 0.92      # 宽松模式 - 更不容易判定为NSFW
        }
        self.current_mode = 'balanced'
        
        self.max_length = 512
        self.overlap_length = 50
        self.min_segment_length = 150
        
        # 模型和分词器
        self.tokenizer = None
        self.model = None
        self.keyword_detector = None
        self.model_loaded_time = None

        # GPU配置
        self.device = None
        self.gpu_available = False

        # 并发配置
        self.max_workers = min(4, multiprocessing.cpu_count())  # 限制并发数
        self.concurrent_enabled = True  # 是否启用并发处理

        # 性能监控
        self.performance_stats = {
            'total_requests': 0,
            'total_processing_time': 0.0,
            'gpu_inference_time': 0.0,
            'keyword_detection_time': 0.0,
            'file_processing_time': 0.0,
            'concurrent_requests': 0,
            'sequential_requests': 0
        }
        
        # 任务状态存储
        self.tasks = {}
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化模型和检测器"""
        try:
            print("🔧 初始化检测服务...")

            # 检测GPU可用性
            self._setup_device()

            # 加载分词器和模型
            print(f"📁 模型路径: {self.model_path}")
            self.tokenizer = BertTokenizer.from_pretrained(self.model_path)
            self.model = BertForSequenceClassification.from_pretrained(self.model_path)

            # 将模型移到指定设备
            self.model = self.model.to(self.device)

            # 记录模型加载时间
            self.model_loaded_time = datetime.now().isoformat()

            # 设置为评估模式
            self.model.eval()

            # 初始化关键词检测器
            self.keyword_detector = KeywordDetector(self.config_path)

            print("✅ 检测服务初始化完成")

        except Exception as e:
            print(f"❌ 检测服务初始化失败: {e}")
            raise e

    def _setup_device(self):
        """设置计算设备（GPU/CPU）"""
        if torch.cuda.is_available():
            self.device = torch.device('cuda')
            self.gpu_available = True
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"🚀 GPU加速已启用: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            self.device = torch.device('cpu')
            self.gpu_available = False
            print("⚠️ GPU不可用，使用CPU进行推理")

    def get_performance_stats(self):
        """获取性能统计信息"""
        stats = self.performance_stats.copy()

        # 计算平均处理时间
        if stats['total_requests'] > 0:
            stats['avg_processing_time'] = stats['total_processing_time'] / stats['total_requests']
            stats['avg_gpu_inference_time'] = stats['gpu_inference_time'] / stats['total_requests']
            stats['avg_keyword_detection_time'] = stats['keyword_detection_time'] / stats['total_requests']
        else:
            stats['avg_processing_time'] = 0.0
            stats['avg_gpu_inference_time'] = 0.0
            stats['avg_keyword_detection_time'] = 0.0

        # 添加GPU信息
        if self.gpu_available and torch.cuda.is_available():
            stats['gpu_info'] = {
                'name': torch.cuda.get_device_name(0),
                'memory_allocated': torch.cuda.memory_allocated(0) / 1024**3,  # GB
                'memory_reserved': torch.cuda.memory_reserved(0) / 1024**3,    # GB
                'memory_total': torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
            }
        else:
            stats['gpu_info'] = None

        # 添加系统信息
        stats['system_info'] = {
            'max_workers': self.max_workers,
            'concurrent_enabled': self.concurrent_enabled,
            'device': str(self.device) if self.device else 'unknown'
        }

        return stats

    def reset_performance_stats(self):
        """重置性能统计"""
        self.performance_stats = {
            'total_requests': 0,
            'total_processing_time': 0.0,
            'gpu_inference_time': 0.0,
            'keyword_detection_time': 0.0,
            'file_processing_time': 0.0,
            'concurrent_requests': 0,
            'sequential_requests': 0
        }

    def _detect_segments_optimized(self, segments, task_id=None, original_text=None):
        """优化的分段检测方法（支持批量推理、GPU加速和文档级关键词检测）"""
        try:
            print(f"🚀 开始优化分段检测，分段数: {len(segments)}, 原始文本长度: {len(original_text) if original_text else 0}")
            segment_results = []
            nsfw_segments = 0
            total_segments = len(segments)

            # 首先对整个原始文档进行关键词检测（如果提供了原始文本）
            document_keyword_result = None
            document_keyword_priority = False

            if original_text and self.keyword_detector:
                print(f"🔍 开始文档级关键词检测，文本长度: {len(original_text)}")
                document_keyword_result = self.keyword_detector.detect(original_text)
                document_keyword_priority = document_keyword_result.get("keyword_priority", False)

                print(f"📝 文档级检测结果: priority={document_keyword_priority}, decision={document_keyword_result.get('final_decision')}")

                # 如果整个文档的关键词检测有明确结果且优先级高
                if document_keyword_priority and document_keyword_result.get("final_decision") is not None:
                    document_decision = document_keyword_result["final_decision"]
                    document_is_nsfw = document_decision == "NSFW"

                    print(f"🔍 整个文档关键词检测结果: {document_decision}")
                    matched_blacklist = document_keyword_result.get('matched_blacklist', [])
                    matched_whitelist = document_keyword_result.get('matched_whitelist', [])
                    matched_advanced_rules = document_keyword_result.get('matched_advanced_rules', [])
                    context_exemptions = document_keyword_result.get('context_exemptions', [])
                    
                    print(f"📝 匹配的黑名单关键词: {matched_blacklist}")
                    print(f"📝 匹配的白名单关键词: {matched_whitelist}")
                    if matched_advanced_rules:
                        print(f"📝 匹配的高级规则: {[rule.get('description', 'Unknown') + ' -> ' + str(rule.get('matched_keywords', [])) for rule in matched_advanced_rules]}")
                    if context_exemptions:
                        print(f"📝 上下文豁免规则: {[exemption.get('rule_name', 'Unknown') for exemption in context_exemptions]}")
                    print(f"📝 关键词优先级: {document_keyword_result.get('keyword_priority', False)}")
                    print(f"📝 最终决定: {document_keyword_result.get('final_decision', None)}")

                    # 对每个分段进行精确的关键词检测，确定哪些分段真正包含关键词
                    for i, segment in enumerate(segments):
                        # 对单个分段进行关键词检测
                        segment_keyword_result = self.keyword_detector.detect(segment)
                        segment_has_keyword = (segment_keyword_result.get("keyword_priority", False) and
                                             segment_keyword_result.get("final_decision") == document_decision)

                        if segment_has_keyword:
                            # 分段本身包含关键词
                            segment_result = {
                                "segment_index": i + 1,
                                "segment_text_full": segment,
                                "segment_text_preview": segment[:100] + "..." if len(segment) > 100 else segment,
                                "segment_length": len(segment),
                                "is_nsfw": document_is_nsfw,
                                "nsfw_probability": 1.0 if document_is_nsfw else 0.0,
                                "detection_method": "keyword",
                                "keyword_detection": segment_keyword_result
                            }

                            if document_is_nsfw:
                                nsfw_segments += 1
                        else:
                            # 分段本身不包含关键词，但由于文档级判断，需要进行模型检测
                            inputs = self.tokenizer(segment, padding=True, truncation=True, max_length=512, return_tensors="pt")
                            inputs = {k: v.to(self.device) for k, v in inputs.items()}

                            with torch.no_grad():
                                outputs = self.model(**inputs)
                                logits = outputs.logits
                                probs = torch.softmax(logits, dim=-1)[:, 1].cpu().numpy()
                                prob = probs[0]
                                segment_is_nsfw = bool(prob > self.threshold)

                            segment_result = {
                                "segment_index": i + 1,
                                "segment_text_full": segment,
                                "segment_text_preview": segment[:100] + "..." if len(segment) > 100 else segment,
                                "segment_length": len(segment),
                                "is_nsfw": segment_is_nsfw,
                                "nsfw_probability": float(round(prob, 4)),
                                "detection_method": "model",
                                "keyword_detection": segment_keyword_result
                            }

                            if segment_is_nsfw:
                                nsfw_segments += 1

                        segment_results.append(segment_result)

                    # 更新任务进度
                    if task_id:
                        self.update_task_progress(task_id, 80)

                    # 整体结果：由于文档级关键词匹配，整体判断为NSFW（即使某些分段可能是SFW）
                    overall_result = {
                        "total_segments": total_segments,
                        "nsfw_segments": nsfw_segments,
                        "nsfw_segment_ratio": round(nsfw_segments / total_segments, 4) if total_segments > 0 else 0,
                        "overall_is_nsfw": document_is_nsfw,  # 文档级判断
                        "overall_probability": 1.0 if document_is_nsfw else 0.0,
                        "detection_strategy": "文档级关键词检测优先（整个文档匹配关键词，分段精确标记）",
                        "document_keyword_result": document_keyword_result
                    }

                    return segment_results, overall_result

            # 如果没有文档级关键词匹配，则进行分段检测
            print("🔍 进行分段级别的检测...")

            # 分离需要模型检测的片段和关键词检测的片段
            model_segments = []
            model_indices = []

            for i, segment in enumerate(segments):
                # 对每个分段进行关键词检测
                keyword_result = self.keyword_detector.detect(segment) if self.keyword_detector else {"keyword_priority": False}

                # 如果分段关键词检测有明确结果且优先级高，直接使用
                if keyword_result.get("keyword_priority", False) and keyword_result.get("final_decision"):
                    is_nsfw = keyword_result["final_decision"] == "NSFW"
                    prob = 1.0 if is_nsfw else 0.0
                    detection_method = "keyword"
                    print(f"📍 分段 {i+1} 关键词匹配: {keyword_result['final_decision']}")

                    segment_result = {
                        "segment_index": i + 1,
                        "segment_text_full": segment,
                        "segment_text_preview": segment[:100] + "..." if len(segment) > 100 else segment,
                        "segment_length": len(segment),
                        "is_nsfw": is_nsfw,
                        "nsfw_probability": float(round(prob, 4)),
                        "detection_method": detection_method,
                        "keyword_detection": keyword_result
                    }

                    segment_results.append((i, segment_result))

                    if is_nsfw:
                        nsfw_segments += 1
                else:
                    # 需要模型检测的片段
                    model_segments.append(segment)
                    model_indices.append(i)

            # 批量处理需要模型检测的片段
            if model_segments:
                model_results = self._batch_model_inference(model_segments)

                for idx, (segment_idx, prob) in enumerate(zip(model_indices, model_results)):
                    segment = model_segments[idx]
                    is_nsfw = bool(prob > self.threshold)

                    # 获取对应的关键词检测结果
                    keyword_result = self.keyword_detector.detect(segment)

                    segment_result = {
                        "segment_index": segment_idx + 1,
                        "segment_text_full": segment,
                        "segment_text_preview": segment[:100] + "..." if len(segment) > 100 else segment,
                        "segment_length": len(segment),
                        "is_nsfw": is_nsfw,
                        "nsfw_probability": float(round(prob, 4)),
                        "detection_method": "model",
                        "keyword_detection": keyword_result
                    }

                    segment_results.append((segment_idx, segment_result))

                    if is_nsfw:
                        nsfw_segments += 1

            # 按原始顺序排序结果
            segment_results.sort(key=lambda x: x[0])
            segment_results = [result[1] for result in segment_results]

            # 更新任务进度
            if task_id:
                self.update_task_progress(task_id, 80)

            # 整体判断策略：任何分段为NSFW则整体为NSFW
            overall_is_nsfw = nsfw_segments > 0
            overall_probability = max([r["nsfw_probability"] for r in segment_results]) if segment_results else 0.0

            overall_result = {
                "total_segments": total_segments,
                "nsfw_segments": nsfw_segments,
                "nsfw_segment_ratio": round(nsfw_segments / total_segments, 4) if total_segments > 0 else 0,
                "overall_is_nsfw": overall_is_nsfw,
                "overall_probability": overall_probability,
                "detection_strategy": "分段检测：GPU加速批量推理 + 关键词优先检测",
                "document_keyword_result": document_keyword_result
            }

            return segment_results, overall_result

        except Exception as e:
            print(f"❌ 优化分段检测失败: {e}")
            import traceback
            traceback.print_exc()
            print("🔄 降级到原始方法")
            # 降级到原始方法
            return detect_text_segments_with_keywords(
                segments, self.tokenizer, self.model, self.threshold, self.keyword_detector, original_text
            )

    def _batch_model_inference(self, segments, batch_size=8):
        """批量模型推理"""
        try:
            results = []

            for i in range(0, len(segments), batch_size):
                batch_segments = segments[i:i+batch_size]

                # 批量分词
                inputs = self.tokenizer(
                    batch_segments,
                    padding=True,
                    truncation=True,
                    max_length=512,
                    return_tensors="pt"
                )

                # 移到指定设备
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

                # 批量推理
                with torch.no_grad():
                    outputs = self.model(**inputs)
                    logits = outputs.logits
                    probs = torch.softmax(logits, dim=-1)[:, 1].cpu().numpy()

                results.extend(probs)

            return results

        except Exception as e:
            print(f"❌ 批量推理失败: {e}")
            # 降级到单个推理
            results = []
            for segment in segments:
                inputs = self.tokenizer(segment, padding=True, truncation=True, max_length=512, return_tensors="pt")
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

                with torch.no_grad():
                    outputs = self.model(**inputs)
                    logits = outputs.logits
                    probs = torch.softmax(logits, dim=-1)[:, 1].cpu().numpy()
                    results.append(probs[0])

            return results
    
    def create_task(self, task_type, input_data, user_id=None):
        """创建检测任务"""
        task_id = str(uuid.uuid4())
        
        task = {
            'id': task_id,
            'type': task_type,
            'status': 'pending',
            'progress': 0,
            'input_data': input_data,
            'result': None,
            'error': None,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'user_id': user_id
        }
        
        self.tasks[task_id] = task
        return task_id
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        return self.tasks.get(task_id, None)
    
    def update_task_progress(self, task_id, progress, status=None):
        """更新任务进度"""
        if task_id in self.tasks:
            self.tasks[task_id]['progress'] = progress
            self.tasks[task_id]['updated_at'] = datetime.now().isoformat()
            if status:
                self.tasks[task_id]['status'] = status
    
    def detect_text(self, text, task_id=None):
        """检测单个文本"""
        start_time = time.time()

        try:
            print(f"🔍 开始检测文本，长度: {len(text)} 字符")
            if task_id:
                self.update_task_progress(task_id, 10, 'running')

            # 分段处理
            segments = split_long_text(
                text,
                self.tokenizer,
                max_length=self.max_length,
                overlap=self.overlap_length,
                min_length=self.min_segment_length
            )

            print(f"📑 文本分段完成，分段数: {len(segments)}")

            if task_id:
                self.update_task_progress(task_id, 30)

            # 检测各个分段（使用优化的批量推理，传递原始文本）
            segment_results, overall_result = self._detect_segments_optimized(
                segments,
                task_id,
                original_text=text
            )

            if task_id:
                self.update_task_progress(task_id, 80)

            # 汇总结果
            result = self._summarize_results(text, segment_results)

            # 更新性能统计
            processing_time = time.time() - start_time
            self.performance_stats['total_requests'] += 1
            self.performance_stats['total_processing_time'] += processing_time

            if task_id:
                self.update_task_progress(task_id, 100, 'completed')
                self.tasks[task_id]['result'] = result

            return result

        except Exception as e:
            if task_id:
                self.tasks[task_id]['status'] = 'failed'
                self.tasks[task_id]['error'] = str(e)
            raise e
    
    def detect_text_async(self, text, task_id):
        """异步检测文本"""
        def run_detection():
            try:
                self.detect_text(text, task_id)
            except Exception as e:
                print(f"❌ 异步检测失败: {e}")
        
        thread = threading.Thread(target=run_detection)
        thread.daemon = True
        thread.start()
    
    def _extract_text_from_file(self, file_path):
        """从不同类型的文件中提取文本内容"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.txt' or file_ext == '.md':
                # 处理文本文件和Markdown文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            elif file_ext == '.pdf':
                # 处理PDF文件
                try:
                    import PyPDF2
                    with open(file_path, 'rb') as f:
                        reader = PyPDF2.PdfReader(f)
                        text = ""
                        for page in reader.pages:
                            text += page.extract_text() + "\n"
                        return text
                except ImportError:
                    # 如果没有PyPDF2，尝试使用pdfplumber
                    try:
                        import pdfplumber
                        with pdfplumber.open(file_path) as pdf:
                            text = ""
                            for page in pdf.pages:
                                text += page.extract_text() + "\n"
                            return text
                    except ImportError:
                        raise Exception("需要安装 PyPDF2 或 pdfplumber 来处理PDF文件")
            
            elif file_ext in ['.doc', '.docx']:
                # 处理Word文档
                try:
                    import docx
                    doc = docx.Document(file_path)
                    text = ""
                    for paragraph in doc.paragraphs:
                        text += paragraph.text + "\n"
                    return text
                except ImportError:
                    raise Exception("需要安装 python-docx 来处理Word文档")
            
            else:
                raise Exception(f"不支持的文件类型: {file_ext}")
                
        except Exception as e:
            raise Exception(f"提取文件内容失败: {str(e)}")

    def detect_batch_files(self, file_paths, task_id=None):
        """批量检测文件（支持并发处理）"""
        try:
            if task_id:
                self.update_task_progress(task_id, 5, 'running')

            total_files = len(file_paths)

            # 根据文件数量决定是否使用并发
            if self.concurrent_enabled and total_files > 3:
                results = self._detect_batch_files_concurrent(file_paths, task_id)
            else:
                results = self._detect_batch_files_sequential(file_paths, task_id)

            # 生成汇总统计
            summary = self._generate_batch_summary(results)

            # 收集所有文件的关键词
            all_matched_keywords = []
            for result in results:
                if result.get('matched_keywords'):
                    all_matched_keywords.extend(result['matched_keywords'])

            batch_result = {
                'summary': summary,
                'details': results,
                'total_files': total_files,
                'matched_keywords': list(set(all_matched_keywords)),  # 去重的所有匹配关键词
                'completed_at': datetime.now().isoformat(),
                'processing_mode': 'concurrent' if (self.concurrent_enabled and total_files > 3) else 'sequential'
            }

            if task_id:
                self.update_task_progress(task_id, 100, 'completed')
                self.tasks[task_id]['result'] = batch_result

            return batch_result

        except Exception as e:
            if task_id:
                self.tasks[task_id]['status'] = 'failed'
                self.tasks[task_id]['error'] = str(e)
            raise e

    def _detect_batch_files_concurrent(self, file_paths, task_id=None):
        """并发批量检测文件"""
        results = []
        total_files = len(file_paths)
        completed_files = 0

        def detect_single_file(file_path):
            """检测单个文件的辅助函数"""
            try:
                # 提取文件内容
                content = self._extract_text_from_file(file_path)

                if not content.strip():
                    raise Exception("文件内容为空")

                # 检测文件
                file_result = self.detect_text(content)
                file_result['file_path'] = file_path
                file_result['file_name'] = os.path.basename(file_path)

                # 调试输出：检查文件结果的关键字段
                print(f"🔍 文件检测结果 - {os.path.basename(file_path)}:")
                print(f"  - final_decision: {file_result.get('final_decision')}")
                print(f"  - confidence: {file_result.get('confidence')}")
                print(f"  - detection_method: {file_result.get('detection_method')}")
                print(f"  - keyword_priority: {file_result.get('keyword_priority')}")
                print(f"  - matched_keywords: {file_result.get('matched_keywords')}")
                print(f"  - original_text_length: {file_result.get('original_text_length')}")

                return file_result

            except Exception as e:
                return {
                    'file_path': file_path,
                    'file_name': os.path.basename(file_path),
                    'error': str(e),
                    'final_decision': 'ERROR'
                }

        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(detect_single_file, file_path): file_path
                for file_path in file_paths
            }

            # 收集结果
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    results.append(result)
                    completed_files += 1

                    # 更新进度
                    if task_id:
                        progress = 10 + int(completed_files / total_files * 80)
                        self.update_task_progress(task_id, progress)

                except Exception as e:
                    error_result = {
                        'file_path': file_path,
                        'file_name': os.path.basename(file_path),
                        'error': str(e),
                        'final_decision': 'ERROR'
                    }
                    results.append(error_result)
                    completed_files += 1

        return results

    def _detect_batch_files_sequential(self, file_paths, task_id=None):
        """顺序批量检测文件（原始方法）"""
        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            try:
                # 提取文件内容
                content = self._extract_text_from_file(file_path)

                if not content.strip():
                    raise Exception("文件内容为空")

                # 检测文件
                file_result = self.detect_text(content)
                file_result['file_path'] = file_path
                file_result['file_name'] = os.path.basename(file_path)

                results.append(file_result)

                # 更新进度
                if task_id:
                    progress = 10 + int((i + 1) / total_files * 80)
                    self.update_task_progress(task_id, progress)

            except Exception as e:
                error_result = {
                    'file_path': file_path,
                    'file_name': os.path.basename(file_path),
                    'error': str(e),
                    'final_decision': 'ERROR'
                }
                results.append(error_result)

        return results
    
    def detect_batch_files_async(self, file_paths, task_id):
        """异步批量检测文件"""
        def run_batch_detection():
            try:
                result = self.detect_batch_files(file_paths, task_id)
                
                # 保存批量检测记录到数据库
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    user_id = task.get('user_id')
                    
                    if user_id and result:
                        # 导入保存函数
                        import sys
                        import os
                        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                        
                        # 为每个文件保存一条记录
                        for file_result in result.get('details', []):
                            try:
                                # 调用app.py中的保存函数
                                from web_api.app import save_detection_record
                                save_detection_record(
                                    user_id=user_id,
                                    task_type='batch_file_detection',
                                    input_data={
                                        'file_name': file_result.get('file_name', ''),
                                        'original_text_length': file_result.get('original_text_length', 0)
                                    },
                                    result_data=file_result,
                                    task_id=f"{task_id}_{file_result.get('file_name', '')}"
                                )
                            except Exception as save_error:
                                print(f"⚠️ 保存文件检测记录失败: {save_error}")
                                
                # 如果是上传文件的批量检测，完成后清理临时目录
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    if task.get('type') == 'batch_upload_detection':
                        temp_dir = task.get('input_data', {}).get('temp_dir')
                        if temp_dir and os.path.exists(temp_dir):
                            import shutil
                            try:
                                shutil.rmtree(temp_dir)
                                print(f"✅ 已清理临时目录: {temp_dir}")
                            except Exception as e:
                                print(f"⚠️ 清理临时目录失败: {e}")
                                
            except Exception as e:
                print(f"❌ 异步批量检测失败: {e}")
                # 即使检测失败也要清理临时目录
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    if task.get('type') == 'batch_upload_detection':
                        temp_dir = task.get('input_data', {}).get('temp_dir')
                        if temp_dir and os.path.exists(temp_dir):
                            import shutil
                            try:
                                shutil.rmtree(temp_dir)
                                print(f"✅ 已清理临时目录: {temp_dir}")
                            except Exception as cleanup_error:
                                print(f"⚠️ 清理临时目录失败: {cleanup_error}")
        
        thread = threading.Thread(target=run_batch_detection)
        thread.daemon = True
        thread.start()
    
    def _summarize_results(self, original_text, segment_results):
        """汇总分段检测结果"""
        # 统计信息
        total_segments = len(segment_results)
        nsfw_segments = sum(1 for r in segment_results if r.get('is_nsfw', False))
        sfw_segments = total_segments - nsfw_segments
        
        # 关键词统计
        keyword_hits = sum(1 for r in segment_results if r.get('detection_method') == 'keyword')
        model_hits = total_segments - keyword_hits
        
        # 最终决策：如果有任何分段是NSFW，整体就是NSFW
        final_decision = 'NSFW' if nsfw_segments > 0 else 'SFW'
        
        # 收集所有匹配的关键词
        all_matched_keywords = []
        for result in segment_results:
            keyword_detection = result.get('keyword_detection', {})
            if keyword_detection.get('matched_blacklist'):
                all_matched_keywords.extend(keyword_detection['matched_blacklist'])
            if keyword_detection.get('matched_whitelist'):
                all_matched_keywords.extend(keyword_detection['matched_whitelist'])
            # 处理高级规则匹配的关键词
            if keyword_detection.get('matched_advanced_rules'):
                for rule in keyword_detection['matched_advanced_rules']:
                    if isinstance(rule, dict) and rule.get('matched_keywords'):
                        all_matched_keywords.extend(rule['matched_keywords'])
        
        # 计算置信度（最高的NSFW概率）
        max_confidence = 0
        if segment_results:
            max_confidence = max(r.get('nsfw_probability', 0) for r in segment_results)
        
        # 检查是否有任何分段使用了关键词检测且优先级为true
        keyword_priority = any(
            result.get('keyword_detection', {}).get('keyword_priority', False)
            for result in segment_results
        )
        
        # 确定最终的检测方法
        final_detection_method = "AI模型检测"
        if keyword_hits > 0 and model_hits > 0:
            final_detection_method = "混合检测"
        elif keyword_hits > 0 and model_hits == 0:
            final_detection_method = "关键词检测"
        elif keyword_hits == 0 and all_matched_keywords:
            final_detection_method = "混合检测"
        
        return {
            'final_decision': final_decision,
            'confidence': max_confidence * 100,  # 转换为百分比
            'detection_method': final_detection_method,
            'keyword_priority': keyword_priority,  # 添加关键词优先级字段
            'statistics': {
                'total_segments': total_segments,
                'nsfw_segments': nsfw_segments,
                'sfw_segments': sfw_segments,
                'keyword_hits': keyword_hits,
                'model_hits': model_hits,
                'keyword_efficiency': (keyword_hits / total_segments * 100) if total_segments > 0 else 0,
                'model_efficiency': (model_hits / total_segments * 100) if total_segments > 0 else 0
            },
            'matched_keywords': list(set(all_matched_keywords)),
            'segment_details': segment_results,
            'original_text_length': len(original_text),
            'detected_at': datetime.now().isoformat()
        }
    
    def _generate_batch_summary(self, results):
        """生成批量检测汇总"""
        total_files = len(results)
        nsfw_count = sum(1 for r in results if r.get('final_decision') == 'NSFW')
        sfw_count = sum(1 for r in results if r.get('final_decision') == 'SFW')
        error_count = sum(1 for r in results if r.get('final_decision') == 'ERROR')
        
        # 收集关键词优先级统计
        keyword_priority_count = sum(1 for r in results if r.get('keyword_priority', False))
        
        return {
            'total_files': total_files,
            'nsfw_count': nsfw_count,
            'sfw_count': sfw_count,
            'error_count': error_count,
            'keyword_priority_count': keyword_priority_count,  # 添加关键词优先级统计
            'nsfw_percentage': (nsfw_count / total_files * 100) if total_files > 0 else 0,
            'sfw_percentage': (sfw_count / total_files * 100) if total_files > 0 else 0,
            'error_percentage': (error_count / total_files * 100) if total_files > 0 else 0
        }
    
    def set_detection_mode(self, mode):
        """设置检测模式"""
        if mode in self.threshold_modes:
            self.current_mode = mode
            self.threshold = self.threshold_modes[mode]
            print(f"🔧 检测模式已切换为: {mode} (阈值: {self.threshold})")
            return True
        return False
    
    def get_detection_config(self):
        """获取当前检测配置"""
        return {
            'current_mode': self.current_mode,
            'current_threshold': self.threshold,
            'available_modes': self.threshold_modes,
            'keyword_enabled': self.keyword_detector.enabled if self.keyword_detector else False
        }

# 全局检测服务实例
detection_service = None

def get_detection_service():
    """获取检测服务实例（单例模式）"""
    global detection_service
    if detection_service is None:
        detection_service = DetectionService()
    return detection_service 