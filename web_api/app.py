#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本内容检测系统 - 后端API
端口: 7000
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
import sqlite3
import hashlib
import os
import sys
from datetime import datetime, timedelta
import json
import threading
import time
import glob

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入检测服务
from web_api.detection_service import get_detection_service
from web_api.keyword_extraction_service import get_keyword_extraction_service, AIClassificationService

app = Flask(__name__)
app.config['JWT_SECRET_KEY'] = 'your-secret-string-here'  # 生产环境需要更改
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# 启用CORS和JWT - 增强配置
CORS(app, 
     resources={r"/*": {"origins": "*"}},
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allow_headers=['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
     supports_credentials=False,
     expose_headers=['Content-Type', 'Authorization'])

jwt = JWTManager(app)

# 数据库配置
DB_PATH = 'web_api/detection_system.db'

def init_database():
    """初始化数据库"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 检测记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS detection_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            task_id TEXT UNIQUE,
            task_type TEXT,
            status TEXT,
            input_data TEXT,
            result_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # 人工标注表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS manual_annotations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            record_id INTEGER,
            text_content TEXT,
            model_prediction TEXT,
            human_label TEXT,
            is_error BOOLEAN,
            annotated_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (record_id) REFERENCES detection_records (id),
            FOREIGN KEY (annotated_by) REFERENCES users (id)
        )
    ''')
    
    # 创建默认管理员用户
    admin_password = hashlib.sha256('admin123'.encode()).hexdigest()
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password_hash) 
        VALUES (?, ?)
    ''', ('admin', admin_password))
    
    conn.commit()
    conn.close()

# 全局变量存储检测任务状态
detection_tasks = {}

# 添加OPTIONS处理器
@app.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = jsonify()
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({'status': 'ok', 'message': '文本内容检测系统API运行正常'})

@app.route('/api/docs/swagger.json', methods=['GET'])
def get_swagger_spec():
    """获取Swagger API规范"""
    swagger_spec = {
        "openapi": "3.0.0",
        "info": {
            "title": "性内容检测系统 API",
            "description": "提供文本内容检测、关键词管理、系统配置等功能的API接口",
            "version": "1.0.0",
            "contact": {
                "name": "API支持",
                "email": "<EMAIL>"
            }
        },
        "servers": [
            {
                "url": "http://localhost:7000",
                "description": "开发服务器"
            }
        ],
        "components": {
            "securitySchemes": {
                "bearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT"
                }
            }
        },
        "paths": {
            "/api/auth/login": {
                "post": {
                    "tags": ["认证"],
                    "summary": "用户登录",
                    "description": "使用用户名和密码进行登录，获取访问令牌",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "required": ["username", "password"],
                                    "properties": {
                                        "username": {"type": "string", "example": "admin"},
                                        "password": {"type": "string", "example": "admin123"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "登录成功",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "access_token": {"type": "string"},
                                            "user_id": {"type": "string"},
                                            "username": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "401": {
                            "description": "用户名或密码错误"
                        }
                    }
                }
            },
            "/api/detect/text": {
                "post": {
                    "tags": ["检测"],
                    "summary": "单文本检测",
                    "description": "检测单个文本内容是否包含NSFW内容",
                    "security": [{"bearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "required": ["text"],
                                    "properties": {
                                        "text": {"type": "string", "example": "要检测的文本内容"},
                                        "detection_method": {"type": "string", "enum": ["auto", "keyword", "model"], "example": "auto"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "检测成功",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "final_decision": {"type": "string", "enum": ["SFW", "NSFW"]},
                                            "confidence": {"type": "number"},
                                            "detection_method": {"type": "string"},
                                            "matched_keywords": {"type": "array", "items": {"type": "string"}},
                                            "segment_details": {"type": "array"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "tags": [
            {"name": "认证", "description": "用户认证相关接口"},
            {"name": "检测", "description": "内容检测相关接口"},
            {"name": "配置", "description": "系统配置相关接口"}
        ]
    }

    return jsonify(swagger_spec)

def save_detection_record(user_id, task_type, input_data, result_data, task_id=None):
    """保存检测记录到数据库"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO detection_records (user_id, task_id, task_type, status, input_data, result_data, completed_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_id,
            task_id or f"sync_{int(time.time() * 1000)}",
            task_type,
            'completed',
            json.dumps(input_data, ensure_ascii=False),
            json.dumps(result_data, ensure_ascii=False),
            datetime.now().isoformat()
        ))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"保存检测记录失败: {e}")
        return False

@app.route('/api/dashboard/stats', methods=['GET'])
@jwt_required()
def get_dashboard_stats():
    """获取仪表板统计数据"""
    try:
        detection_service = get_detection_service()
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 获取今日检测数量
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute("""
            SELECT COUNT(*) FROM detection_records 
            WHERE DATE(created_at) = ?
        """, (today,))
        today_detections = cursor.fetchone()[0]
        
        # 获取安全内容数量（今日）
        cursor.execute("""
            SELECT COUNT(*) FROM detection_records 
            WHERE DATE(created_at) = ? AND result_data LIKE '%"final_decision": "SFW"%'
        """, (today,))
        safe_content = cursor.fetchone()[0]
        
        # 获取有害内容数量（今日）
        cursor.execute("""
            SELECT COUNT(*) FROM detection_records 
            WHERE DATE(created_at) = ? AND result_data LIKE '%"final_decision": "NSFW"%'
        """, (today,))
        harmful_content = cursor.fetchone()[0]
        
        # 获取处理中的任务数量
        processing_tasks = len([task for task in detection_service.tasks.values() 
                               if task.get('status') in ['pending', 'running']])
        
        # 获取总检测数量
        cursor.execute("SELECT COUNT(*) FROM detection_records")
        total_detections = cursor.fetchone()[0]
        
        # 获取模型信息
        model_info = {
            'name': 'BERT文本分类模型',
            'version': '1.0',
            'loaded_at': detection_service.model_loaded_time if hasattr(detection_service, 'model_loaded_time') else None,
            'status': 'loaded' if detection_service.model else 'not_loaded'
        }
        
        # 获取关键词检测状态
        keyword_status = {
            'enabled': detection_service.keyword_detector.enabled if detection_service.keyword_detector else False,
            'whitelist_count': 0,
            'blacklist_count': 0
        }

        if detection_service.keyword_detector and detection_service.keyword_detector.config:
            # 安全获取白名单关键词数量
            whitelist_config = detection_service.keyword_detector.config.get('whitelist')
            if isinstance(whitelist_config, dict):
                whitelist_keywords = whitelist_config.get('keywords')
                if isinstance(whitelist_keywords, (list, tuple)):
                    keyword_status['whitelist_count'] = len(whitelist_keywords)

            # 安全获取黑名单关键词数量
            blacklist_config = detection_service.keyword_detector.config.get('blacklist')
            if isinstance(blacklist_config, dict):
                blacklist_keywords = blacklist_config.get('keywords')
                if isinstance(blacklist_keywords, (list, tuple)):
                    keyword_status['blacklist_count'] = len(blacklist_keywords)
        
        conn.close()
        
        return jsonify({
            'today_detections': today_detections,
            'safe_content': safe_content,
            'harmful_content': harmful_content,
            'processing_tasks': processing_tasks,
            'total_detections': total_detections,
            'model_info': model_info,
            'keyword_status': keyword_status,
            'system_status': {
                'detection_service': 'running',
                'database': 'connected',
                'keyword_detection': 'enabled' if keyword_status['enabled'] else 'disabled',
                'ai_model': model_info['status']
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/test_frontend_detection.html', methods=['GET'])
def test_frontend_detection():
    """前端检测功能测试页面"""
    try:
        test_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'test_frontend_detection.html')
        with open(test_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content, 200, {'Content-Type': 'text/html; charset=utf-8'}
    except Exception as e:
        return f'测试页面加载失败: {e}', 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': '用户名和密码不能为空'}), 400
    
    # 验证用户
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    cursor.execute('SELECT id, username FROM users WHERE username = ? AND password_hash = ?', 
                   (username, password_hash))
    user = cursor.fetchone()
    conn.close()
    
    if user:
        access_token = create_access_token(identity=str(user[0]))
        return jsonify({
            'access_token': access_token,
            'user': {'id': user[0], 'username': user[1]}
        })
    else:
        return jsonify({'error': '用户名或密码错误'}), 401

@app.route('/api/auth/register', methods=['POST'])
def register():
    """用户注册"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': '用户名和密码不能为空'}), 400
    
    if len(password) < 6:
        return jsonify({'error': '密码长度不能少于6位'}), 400
    
    # 创建用户
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    try:
        cursor.execute('INSERT INTO users (username, password_hash) VALUES (?, ?)', 
                       (username, password_hash))
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        access_token = create_access_token(identity=str(user_id))
        return jsonify({
            'access_token': access_token,
            'user': {'id': user_id, 'username': username}
        })
    except sqlite3.IntegrityError:
        conn.close()
        return jsonify({'error': '用户名已存在'}), 400

@app.route('/api/user/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户信息"""
    user_id = int(get_jwt_identity())
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('SELECT id, username, created_at FROM users WHERE id = ?', (user_id,))
    user = cursor.fetchone()
    conn.close()
    
    if user:
        return jsonify({
            'id': user[0],
            'username': user[1],
            'created_at': user[2]
        })
    else:
        return jsonify({'error': '用户不存在'}), 404

# ===================检测相关API===================

@app.route('/api/detect/text', methods=['POST'])
@jwt_required()
def detect_text():
    """检测文本内容"""
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        async_mode = data.get('async', False)
        
        if not text:
            return jsonify({'error': '文本内容不能为空'}), 400
        
        user_id = int(get_jwt_identity())
        detection_service = get_detection_service()
        
        if async_mode:
            # 异步模式
            task_id = detection_service.create_task('text_detection', {'text': text}, user_id)
            
            # 创建异步检测线程，完成后保存记录
            def async_detection_with_save():
                try:
                    result = detection_service.detect_text(text, task_id)
                    # 保存检测记录
                    save_detection_record(
                        user_id=user_id,
                        task_type='text_detection',
                        input_data={'text': text},
                        result_data=result,
                        task_id=task_id
                    )
                except Exception as e:
                    print(f"❌ 异步检测失败: {e}")
            
            thread = threading.Thread(target=async_detection_with_save)
            thread.daemon = True
            thread.start()
            
            return jsonify({
                'task_id': task_id,
                'status': 'pending',
                'message': '检测任务已创建，请使用task_id查询结果'
            })
        else:
            # 同步模式
            result = detection_service.detect_text(text)
            
            # 保存检测记录
            save_detection_record(
                user_id=user_id,
                task_type='text_detection',
                input_data={'text': text},
                result_data=result
            )
            
            return jsonify({
                'status': 'completed',
                'result': result
            })
            
    except Exception as e:
        return jsonify({'error': f'检测失败: {str(e)}'}), 500

@app.route('/api/detect/batch', methods=['POST'])
@jwt_required()
def detect_batch():
    """批量检测文件"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path', '').strip()
        file_pattern = data.get('file_pattern', '*.txt')
        async_mode = data.get('async', True)  # 批量检测默认异步
        
        if not folder_path:
            return jsonify({'error': '文件夹路径不能为空'}), 400
        
        if not os.path.exists(folder_path):
            return jsonify({'error': '文件夹不存在'}), 400
        
        # 获取文件列表
        file_paths = glob.glob(os.path.join(folder_path, file_pattern))
        if not file_paths:
            return jsonify({'error': f'在{folder_path}中未找到匹配{file_pattern}的文件'}), 400
        
        user_id = int(get_jwt_identity())
        detection_service = get_detection_service()
        
        if async_mode:
            # 异步模式
            task_id = detection_service.create_task('batch_detection', {
                'folder_path': folder_path,
                'file_pattern': file_pattern,
                'file_count': len(file_paths)
            }, user_id)
            detection_service.detect_batch_files_async(file_paths, task_id)
            
            return jsonify({
                'task_id': task_id,
                'status': 'pending',
                'file_count': len(file_paths),
                'message': '批量检测任务已创建，请使用task_id查询结果'
            })
        else:
            # 同步模式（不推荐用于大量文件）
            if len(file_paths) > 10:
                return jsonify({'error': '同步模式最多支持10个文件，请使用异步模式'}), 400
            
            result = detection_service.detect_batch_files(file_paths)
            return jsonify({
                'status': 'completed',
                'result': result
            })
            
    except Exception as e:
        return jsonify({'error': f'批量检测失败: {str(e)}'}), 500

@app.route('/api/detect/batch-upload', methods=['POST'])
@jwt_required()
def detect_batch_upload():
    """批量检测上传的文件"""
    try:
        # 检查是否有文件上传
        if 'files' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400
        
        files = request.files.getlist('files')
        if not files or len(files) == 0:
            return jsonify({'error': '文件列表为空'}), 400
        
        # 创建临时目录存储上传的文件
        import tempfile
        import shutil
        temp_dir = tempfile.mkdtemp(prefix='batch_upload_')
        
        try:
            uploaded_files = []
            
            # 保存上传的文件到临时目录
            for file in files:
                if not file.filename or file.filename == '':
                    continue

                # 检查文件扩展名
                allowed_extensions = {'.txt', '.doc', '.docx', '.pdf', '.md'}
                file_ext = os.path.splitext(file.filename)[1].lower()

                if file_ext not in allowed_extensions:
                    return jsonify({'error': f'不支持的文件类型: {file.filename}'}), 400

                # 生成安全的文件名
                import uuid
                safe_filename = f"{uuid.uuid4().hex}_{file.filename}"
                file_path = os.path.join(temp_dir, safe_filename)
                
                # 保存文件
                file.save(file_path)
                uploaded_files.append(file_path)
            
            if not uploaded_files:
                return jsonify({'error': '没有有效的文件'}), 400
            
            user_id = int(get_jwt_identity())
            detection_service = get_detection_service()
            
            # 创建异步检测任务
            task_id = detection_service.create_task('batch_upload_detection', {
                'temp_dir': temp_dir,
                'file_count': len(uploaded_files),
                'original_filenames': [f.filename for f in files if f.filename != '']
            }, user_id)
            
            # 启动异步检测
            detection_service.detect_batch_files_async(uploaded_files, task_id)
            
            return jsonify({
                'task_id': task_id,
                'status': 'pending',
                'file_count': len(uploaded_files),
                'message': '文件上传成功，批量检测任务已创建'
            })
            
        except Exception as e:
            # 清理临时目录
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            raise e
            
    except Exception as e:
        return jsonify({'error': f'文件上传批量检测失败: {str(e)}'}), 500

@app.route('/api/detect/task/<task_id>', methods=['GET'])
@jwt_required()
def get_task_status(task_id):
    """获取检测任务状态"""
    try:
        detection_service = get_detection_service()
        task = detection_service.get_task_status(task_id)
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        # 检查任务所有权
        user_id = int(get_jwt_identity())
        if task.get('user_id') != user_id:
            return jsonify({'error': '无权访问此任务'}), 403
        
        return jsonify(task)
        
    except Exception as e:
        return jsonify({'error': f'获取任务状态失败: {str(e)}'}), 500

@app.route('/api/detect/folders', methods=['GET'])
@jwt_required()
def list_available_folders():
    """列出可用的检测文件夹"""
    try:
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        available_folders = []
        
        # 添加常用的检测目录
        common_folders = [
            'content',
            'micro_adjustment_data/NSFW',
            'micro_adjustment_data/SFW',
            'micro_adjustment_data/Validation-NSFW',
            'micro_adjustment_data/Validation-SFW',
            'test'
        ]
        
        for folder in common_folders:
            folder_path = os.path.join(base_dir, folder)
            if os.path.exists(folder_path):
                # 统计文件数量
                txt_files = glob.glob(os.path.join(folder_path, '*.txt'))
                available_folders.append({
                    'name': folder,
                    'path': folder_path,
                    'file_count': len(txt_files),
                    'description': f'{folder} 目录'
                })
        
        return jsonify({'folders': available_folders})
        
    except Exception as e:
        return jsonify({'error': f'获取文件夹列表失败: {str(e)}'}), 500

@app.route('/api/config/keywords', methods=['GET'])
@jwt_required()
def get_keyword_config():
    """获取关键词配置"""
    try:
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(base_dir, 'model', 'keyword_config.json')
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return jsonify(config)
        else:
            return jsonify({'error': '关键词配置文件不存在'}), 404
            
    except Exception as e:
        return jsonify({'error': f'获取关键词配置失败: {str(e)}'}), 500

@app.route('/api/config/keywords', methods=['POST'])
@jwt_required()
def update_keyword_config():
    """更新关键词配置"""
    try:
        data = request.get_json()
        
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(base_dir, 'model', 'keyword_config.json')
        
        # 备份原配置
        if os.path.exists(config_path):
            backup_path = config_path + '.backup.' + datetime.now().strftime('%Y%m%d_%H%M%S')
            os.rename(config_path, backup_path)
        
        # 保存新配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 重新初始化检测服务中的关键词检测器
        detection_service = get_detection_service()
        from model.batch_detect_with_keywords import KeywordDetector
        detection_service.keyword_detector = KeywordDetector(config_path)
        
        return jsonify({'message': '关键词配置更新成功'})

    except Exception as e:
        return jsonify({'error': f'更新关键词配置失败: {str(e)}'}), 500

@app.route('/api/system/performance', methods=['GET'])
@jwt_required()
def get_performance_stats():
    """获取系统性能统计"""
    try:
        detection_service = get_detection_service()
        stats = detection_service.get_performance_stats()

        return jsonify({
            'status': 'success',
            'performance_stats': stats,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': f'获取性能统计失败: {str(e)}'}), 500

@app.route('/api/system/performance/reset', methods=['POST'])
@jwt_required()
def reset_performance_stats():
    """重置性能统计"""
    try:
        detection_service = get_detection_service()
        detection_service.reset_performance_stats()

        return jsonify({
            'status': 'success',
            'message': '性能统计已重置',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': f'重置性能统计失败: {str(e)}'}), 500

@app.route('/api/config/detection', methods=['GET'])
@jwt_required()
def get_detection_config():
    """获取检测参数配置"""
    try:
        detection_service = get_detection_service()
        config = {
            'threshold': detection_service.threshold,
            'max_length': detection_service.max_length,
            'overlap_length': detection_service.overlap_length,
            'min_segment_length': detection_service.min_segment_length
        }
        return jsonify(config)
    except Exception as e:
        return jsonify({'error': f'获取检测参数失败: {str(e)}'}), 500

@app.route('/api/config/detection', methods=['POST'])
@jwt_required()
def update_detection_config():
    """更新检测参数配置"""
    try:
        data = request.get_json()
        
        # 验证参数
        threshold = data.get('threshold', 0.85)
        max_length = data.get('max_length', 512)
        overlap_length = data.get('overlap_length', 50)
        min_segment_length = data.get('min_segment_length', 150)
        
        if not (0 <= threshold <= 1):
            return jsonify({'error': '阈值必须在0-1之间'}), 400
        
        # 支持更小的max_length值，适合短文本精细检测
        if not (30 <= max_length <= 1024):  # 从64改为30，支持短文本检测
            return jsonify({'error': '最大长度必须在30-1024之间'}), 400

        if not (0 <= overlap_length <= 200):
            return jsonify({'error': '重叠长度必须在0-200之间'}), 400

        # 支持更小的min_segment_length值
        if not (10 <= min_segment_length <= 500):  # 从30改为10，支持更精细分段
            return jsonify({'error': '最小分段长度必须在10-500之间'}), 400
        
        # 更新检测服务参数
        detection_service = get_detection_service()
        detection_service.threshold = threshold
        detection_service.max_length = max_length
        detection_service.overlap_length = overlap_length
        detection_service.min_segment_length = min_segment_length
        
        return jsonify({'message': '检测参数更新成功'})
        
    except Exception as e:
        return jsonify({'error': f'更新检测参数失败: {str(e)}'}), 500

@app.route('/api/config/detection/mode', methods=['GET'])
@jwt_required()
def get_detection_mode():
    """获取当前检测模式"""
    try:
        detection_service = get_detection_service()
        config = detection_service.get_detection_config()
        return jsonify(config)
    except Exception as e:
        return jsonify({'error': f'获取检测模式失败: {str(e)}'}), 500

@app.route('/api/config/detection/mode', methods=['POST'])
@jwt_required()
def set_detection_mode():
    """设置检测模式"""
    try:
        data = request.get_json()
        mode = data.get('mode')
        
        if not mode:
            return jsonify({'error': '检测模式不能为空'}), 400
        
        detection_service = get_detection_service()
        
        if detection_service.set_detection_mode(mode):
            return jsonify({
                'message': f'检测模式已切换为: {mode}',
                'current_config': detection_service.get_detection_config()
            })
        else:
            available_modes = list(detection_service.threshold_modes.keys())
            return jsonify({
                'error': f'无效的检测模式: {mode}',
                'available_modes': available_modes
            }), 400
        
    except Exception as e:
        return jsonify({'error': f'设置检测模式失败: {str(e)}'}), 500

@app.route('/api/detect/test', methods=['POST'])
@jwt_required()
def test_detection():
    """测试检测功能 - 支持多种模式对比"""
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        compare_modes = data.get('compare_modes', False)
        
        if not text:
            return jsonify({'error': '文本内容不能为空'}), 400
        
        detection_service = get_detection_service()
        
        if compare_modes:
            # 对比多种模式的检测结果
            results = {}
            original_mode = detection_service.current_mode
            
            for mode in detection_service.threshold_modes.keys():
                detection_service.set_detection_mode(mode)
                result = detection_service.detect_text(text)
                results[mode] = {
                    'threshold': detection_service.threshold,
                    'final_decision': result['final_decision'],
                    'confidence': result['confidence'],
                    'detection_method': result['segment_details'][0]['detection_method'] if result['segment_details'] else 'unknown'
                }
            
            # 恢复原模式
            detection_service.set_detection_mode(original_mode)
            
            return jsonify({
                'text': text,
                'mode_comparison': results,
                'recommendation': _get_mode_recommendation(results)
            })
        else:
            # 单一模式检测
            result = detection_service.detect_text(text)
            return jsonify({
                'text': text,
                'current_mode': detection_service.current_mode,
                'result': result
            })
            
    except Exception as e:
        return jsonify({'error': f'测试检测失败: {str(e)}'}), 500

def _get_mode_recommendation(results):
    """根据检测结果提供模式推荐"""
    decisions = [result['final_decision'] for result in results.values()]
    
    if all(d == 'SFW' for d in decisions):
        return {
            'message': '所有模式都判定为SFW，内容安全',
            'suggested_mode': 'balanced'
        }
    elif all(d == 'NSFW' for d in decisions):
        return {
            'message': '所有模式都判定为NSFW，内容存在问题',
            'suggested_mode': 'balanced'
        }
    else:
        nsfw_modes = [mode for mode, result in results.items() if result['final_decision'] == 'NSFW']
        return {
            'message': f'检测结果不一致，{nsfw_modes}模式判定为NSFW',
            'suggested_mode': 'balanced',
            'note': '建议根据业务场景选择合适的检测模式'
        }

# ===================关键词提取API===================

@app.route('/api/keyword-extraction/upload', methods=['POST'])
@jwt_required()
def upload_extraction_files():
    """上传文件用于关键词提取"""
    try:
        # 检查是否有文件上传
        if 'files' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        files = request.files.getlist('files')
        if not files or len(files) == 0:
            return jsonify({'error': '文件列表为空'}), 400

        # 获取任务名称
        task_name = request.form.get('task_name', '')

        # 创建临时目录存储上传的文件
        import tempfile
        import shutil
        temp_dir = tempfile.mkdtemp(prefix='keyword_extraction_')

        try:
            user_id = int(get_jwt_identity())
            extraction_service = get_keyword_extraction_service()

            # 创建提取任务
            task_id = extraction_service.create_task(user_id, task_name)

            uploaded_files = []

            # 保存上传的文件到临时目录
            for file in files:
                if not file.filename or file.filename == '':
                    continue

                # 检查文件扩展名
                allowed_extensions = {'.txt', '.xlsx', '.xls'}
                file_ext = os.path.splitext(file.filename)[1].lower()

                if file_ext not in allowed_extensions:
                    return jsonify({'error': f'不支持的文件类型: {file.filename}'}), 400

                # 生成安全的文件名
                import uuid
                safe_filename = f"{uuid.uuid4().hex}_{file.filename}"
                file_path = os.path.join(temp_dir, safe_filename)

                # 保存文件
                file.save(file_path)

                uploaded_files.append({
                    'original_filename': file.filename,
                    'file_path': file_path,
                    'file_size': os.path.getsize(file_path),
                    'file_type': file_ext
                })

            if not uploaded_files:
                return jsonify({'error': '没有有效的文件'}), 400

            # 添加文件到任务
            extraction_service.add_files_to_task(task_id, uploaded_files)

            return jsonify({
                'task_id': task_id,
                'status': 'pending',
                'file_count': len(uploaded_files),
                'message': '文件上传成功，关键词提取任务已创建'
            })

        except Exception as e:
            # 清理临时目录
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            raise e

    except Exception as e:
        return jsonify({'error': f'文件上传失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/start/<task_id>', methods=['POST'])
@jwt_required()
def start_extraction_task(task_id):
    """启动关键词提取任务"""
    try:
        user_id = int(get_jwt_identity())
        extraction_service = get_keyword_extraction_service()

        # 检查任务是否存在且属于当前用户
        task = extraction_service.get_task_status(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        if task['user_id'] != user_id:
            return jsonify({'error': '无权访问此任务'}), 403

        if task['status'] != 'pending':
            return jsonify({'error': f'任务状态不允许启动: {task["status"]}'}), 400

        # 获取配置参数
        data = request.get_json() or {}
        config = {
            'min_frequency': data.get('min_frequency', 2),
            'min_word_length': data.get('min_word_length', 2),
            'max_word_length': data.get('max_word_length', 20)
        }

        # 启动提取任务
        extraction_service.start_extraction_task(task_id, config)

        return jsonify({
            'task_id': task_id,
            'status': 'processing',
            'message': '关键词提取任务已启动'
        })

    except Exception as e:
        return jsonify({'error': f'启动提取任务失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/task/<task_id>', methods=['GET'])
@jwt_required()
def get_extraction_task_status(task_id):
    """获取关键词提取任务状态"""
    try:
        user_id = int(get_jwt_identity())
        extraction_service = get_keyword_extraction_service()

        task = extraction_service.get_task_status(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        if task['user_id'] != user_id:
            return jsonify({'error': '无权访问此任务'}), 403

        return jsonify(task)

    except Exception as e:
        return jsonify({'error': f'获取任务状态失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/tasks', methods=['GET'])
@jwt_required()
def get_extraction_tasks():
    """获取用户的关键词提取任务列表"""
    try:
        user_id = int(get_jwt_identity())
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))

        extraction_service = get_keyword_extraction_service()
        result = extraction_service.get_user_tasks(user_id, page, page_size)

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': f'获取任务列表失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/tasks/<task_id>', methods=['DELETE'])
@jwt_required()
def delete_extraction_task(task_id):
    """删除关键词提取任务"""
    try:
        user_id = int(get_jwt_identity())
        extraction_service = get_keyword_extraction_service()

        # 执行删除操作
        success = extraction_service.delete_task(task_id, user_id)

        if success:
            return jsonify({'message': '任务删除成功'})
        else:
            return jsonify({'error': '删除失败，任务不存在、无权限或任务正在运行'}), 400

    except Exception as e:
        return jsonify({'error': f'删除任务失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/words/<task_id>', methods=['GET'])
@jwt_required()
def get_extraction_words(task_id):
    """获取关键词提取结果"""
    try:
        user_id = int(get_jwt_identity())
        extraction_service = get_keyword_extraction_service()

        # 检查任务权限
        task = extraction_service.get_task_status(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        if task['user_id'] != user_id:
            return jsonify({'error': '无权访问此任务'}), 403

        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        filter_type = request.args.get('filter_type', 'all')
        sort_by = request.args.get('sort_by', 'frequency')

        result = extraction_service.get_word_frequency(task_id, page, page_size, filter_type, sort_by)

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': f'获取词频结果失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/ai-classify', methods=['POST'])
@jwt_required()
def ai_classify_words():
    """AI分类词汇"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        words = data.get('words', [])
        async_mode = data.get('async', True)  # 默认使用异步模式

        if not task_id:
            return jsonify({'error': '任务ID不能为空'}), 400

        if not words:
            return jsonify({'error': '词汇列表不能为空'}), 400

        user_id = int(get_jwt_identity())
        extraction_service = get_keyword_extraction_service()

        # 检查任务权限
        task = extraction_service.get_task_status(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        if task['user_id'] != user_id:
            return jsonify({'error': '无权访问此任务'}), 403

        # 获取AI配置
        ai_config = data.get('ai_config', {})

        if async_mode and len(words) > 20:
            # 异步模式 - 适用于大量词汇
            def async_ai_classification():
                try:
                    ai_service = AIClassificationService(ai_config)
                    classifications = ai_service.classify_words_batch(words)

                    # 更新数据库中的AI分类结果
                    conn = sqlite3.connect(DB_PATH)
                    cursor = conn.cursor()

                    for result in classifications:
                        cursor.execute('''
                            UPDATE word_frequency
                            SET ai_classification = ?, ai_confidence = ?
                            WHERE task_id = ? AND word = ?
                        ''', (result['category'], result.get('confidence', 0.0), task_id, result['word']))

                    conn.commit()
                    conn.close()
                    print(f"✅ 异步AI分类完成: {len(classifications)} 个词汇")

                except Exception as e:
                    print(f"❌ 异步AI分类失败: {e}")

            # 启动异步处理线程
            import threading
            thread = threading.Thread(target=async_ai_classification)
            thread.daemon = True
            thread.start()

            return jsonify({
                'status': 'processing',
                'message': f'AI分类任务已启动，正在后台处理 {len(words)} 个词汇',
                'word_count': len(words),
                'async': True
            })

        else:
            # 同步模式 - 适用于少量词汇
            ai_service = AIClassificationService(ai_config)
            classifications = ai_service.classify_words_batch(words)

            # 更新数据库中的AI分类结果
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            for result in classifications:
                cursor.execute('''
                    UPDATE word_frequency
                    SET ai_classification = ?, ai_confidence = ?
                    WHERE task_id = ? AND word = ?
                ''', (result['category'], result.get('confidence', 0.0), task_id, result['word']))

            conn.commit()
            conn.close()

            return jsonify({
                'status': 'completed',
                'message': f'成功分类 {len(classifications)} 个词汇',
                'classifications': classifications,
                'async': False
            })

    except Exception as e:
        return jsonify({'error': f'AI分类失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/ai-providers', methods=['GET'])
def get_ai_providers():
    """获取可用的AI提供商列表"""
    try:
        # 读取AI配置
        config_path = os.path.join(os.path.dirname(__file__), 'ai_config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        providers = []
        for provider_id, provider_config in config['providers'].items():
            providers.append({
                'id': provider_id,
                'name': provider_config['name'],
                'model': provider_config.get('model', ''),
                'batch_size': provider_config.get('batch_size', 20),
                'confidence_threshold': provider_config.get('confidence_threshold', 0.7)
            })

        return jsonify({
            'providers': providers,
            'default_provider': config.get('default_provider', 'mock')
        })

    except Exception as e:
        return jsonify({'error': f'获取AI提供商列表失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/confirm', methods=['POST'])
@jwt_required()
def confirm_words():
    """人工确认词汇分类"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        confirmations = data.get('confirmations', [])

        if not task_id:
            return jsonify({'error': '任务ID不能为空'}), 400

        if not confirmations:
            return jsonify({'error': '确认列表不能为空'}), 400

        user_id = int(get_jwt_identity())
        extraction_service = get_keyword_extraction_service()

        # 检查任务权限
        task = extraction_service.get_task_status(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        if task['user_id'] != user_id:
            return jsonify({'error': '无权访问此任务'}), 403

        # 更新人工确认结果
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        confirmed_count = 0
        for confirmation in confirmations:
            word = confirmation.get('word')
            label = confirmation.get('label')  # nsfw, sfw, reject

            if word and label:
                cursor.execute('''
                    UPDATE word_frequency
                    SET human_confirmed = TRUE, human_label = ?
                    WHERE task_id = ? AND word = ?
                ''', (label, task_id, word))
                confirmed_count += 1

        conn.commit()
        conn.close()

        return jsonify({
            'message': f'成功确认 {confirmed_count} 个词汇',
            'confirmed_count': confirmed_count
        })

    except Exception as e:
        return jsonify({'error': f'人工确认失败: {str(e)}'}), 500

@app.route('/api/keyword-extraction/export/<task_id>', methods=['GET'])
@jwt_required()
def export_keywords(task_id):
    """导出关键词库"""
    try:
        user_id = int(get_jwt_identity())
        extraction_service = get_keyword_extraction_service()

        # 检查任务权限
        task = extraction_service.get_task_status(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        if task['user_id'] != user_id:
            return jsonify({'error': '无权访问此任务'}), 403

        # 获取导出参数
        export_type = request.args.get('type', 'confirmed')  # confirmed, all, nsfw_only
        format_type = request.args.get('format', 'json')  # json, txt, excel

        # 构建查询条件
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        where_clause = "WHERE task_id = ?"
        params = [task_id]

        if export_type == 'confirmed':
            where_clause += " AND human_confirmed = TRUE"
        elif export_type == 'nsfw_only':
            where_clause += " AND (human_label = 'nsfw' OR (ai_classification = 'nsfw' AND human_confirmed = FALSE))"

        cursor.execute(f'''
            SELECT word, frequency, ai_classification, ai_confidence, human_label, human_confirmed
            FROM word_frequency
            {where_clause}
            ORDER BY frequency DESC
        ''', params)

        words = cursor.fetchall()
        conn.close()

        # 格式化导出数据
        export_data = []
        for word in words:
            export_data.append({
                'word': word[0],
                'frequency': word[1],
                'ai_classification': word[2],
                'ai_confidence': word[3],
                'human_label': word[4],
                'human_confirmed': bool(word[5]),
                'final_category': word[4] if word[5] else word[2]  # 人工标注优先
            })

        if format_type == 'json':
            return jsonify({
                'task_id': task_id,
                'export_type': export_type,
                'total_count': len(export_data),
                'keywords': export_data
            })
        elif format_type == 'txt':
            # 返回纯文本格式
            lines = []
            for item in export_data:
                if item['final_category'] == 'nsfw':
                    lines.append(f"{item['word']}\t{item['frequency']}")

            from flask import Response
            return Response(
                '\n'.join(lines),
                mimetype='text/plain',
                headers={'Content-Disposition': f'attachment; filename=keywords_{task_id}.txt'}
            )
        else:
            return jsonify({'error': '不支持的导出格式'}), 400

    except Exception as e:
        return jsonify({'error': f'导出关键词失败: {str(e)}'}), 500

@app.route('/api/keyword-library', methods=['GET'])
@jwt_required()
def get_keyword_library():
    """获取关键词库"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        category = request.args.get('category', 'all')  # all, nsfw, sfw

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 构建查询条件
        where_clause = "WHERE status = 'active'"
        params = []

        if category != 'all':
            where_clause += " AND category = ?"
            params.append(category)

        # 获取总数
        cursor.execute(f'''
            SELECT COUNT(*) FROM keyword_library {where_clause}
        ''', params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        cursor.execute(f'''
            SELECT id, keyword, category, frequency, confidence, created_at
            FROM keyword_library
            {where_clause}
            ORDER BY frequency DESC
            LIMIT ? OFFSET ?
        ''', params + [page_size, offset])

        keywords = cursor.fetchall()
        conn.close()

        return jsonify({
            'keywords': [
                {
                    'id': kw[0],
                    'keyword': kw[1],
                    'category': kw[2],
                    'frequency': kw[3],
                    'confidence': kw[4],
                    'created_at': kw[5]
                } for kw in keywords
            ],
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        })

    except Exception as e:
        return jsonify({'error': f'获取关键词库失败: {str(e)}'}), 500

if __name__ == '__main__':
    # 确保目录存在
    os.makedirs('web_api', exist_ok=True)
    
    # 初始化数据库
    init_database()
    
    print("🚀 文本内容检测系统API启动中...")
    print("📡 API地址: http://localhost:7000")
    print("👤 默认管理员: admin / admin123")
    
    app.run(host='0.0.0.0', port=7000, debug=True) 
