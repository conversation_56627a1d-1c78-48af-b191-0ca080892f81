#\!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '/root/semantic_analysis/sexual_content_dection/model/bert_env/lib/python3.10/site-packages')
from transformers import AutoTokenizer

# 测试不同的decode参数
tokenizer = AutoTokenizer.from_pretrained('/root/semantic_analysis/sexual_content_dection/model/')

test_text = '这是测试文本'
tokens = tokenizer.encode(test_text, add_special_tokens=False)

print(f"原文: {repr(test_text)}")
print(f"tokens: {tokens}")
print()

# 测试不同的decode参数
print("=== 不同decode参数测试 ===")

# 默认decode
decoded1 = tokenizer.decode(tokens)
print(f"默认decode: {repr(decoded1)}")

# 使用skip_special_tokens参数
decoded2 = tokenizer.decode(tokens, skip_special_tokens=True)
print(f"skip_special_tokens=True: {repr(decoded2)}")

# 使用clean_up_tokenization_spaces参数
decoded3 = tokenizer.decode(tokens, clean_up_tokenization_spaces=False)
print(f"clean_up_tokenization_spaces=False: {repr(decoded3)}")

# 两个参数一起使用
decoded4 = tokenizer.decode(tokens, skip_special_tokens=True, clean_up_tokenization_spaces=False)
print(f"两个参数都设置: {repr(decoded4)}")

print()
print("=== 结果对比 ===")
for i, (name, result) in enumerate([
    ("默认", decoded1),
    ("skip_special_tokens=True", decoded2),
    ("clean_up_tokenization_spaces=False", decoded3),
    ("两个参数都设置", decoded4)
], 1):
    print(f"{i}. {name}: 空格数量={result.count(' ')}, 长度={len(result)}")
